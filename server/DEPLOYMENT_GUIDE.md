# Mem0 数据持久化与Docker卷重构部署指南

## 📋 概述

本指南介绍了 Mem0 项目的全新数据持久化架构，采用"混合但清晰可控"的 Docker 卷方案，解决了原有配置中的路径冲突问题，确保数据安全和系统稳定性。

## 🎯 解决的问题

### 原有问题
- **路径冲突**：代码挂载覆盖数据目录
- **权限混乱**：容器内外权限不一致
- **数据风险**：数据可能丢失或损坏
- **迁移困难**：重构时数据不安全

### 新方案优势
- ✅ **职责分离**：代码与数据完全分离
- ✅ **权限自动化**：启动时自动修复权限
- ✅ **数据安全**：完整的备份恢复机制
- ✅ **环境分离**：开发/生产环境清晰分离
- ✅ **迁移友好**：安全的数据迁移流程

## 🏗️ 架构设计

### 目录结构
```
/opt/mem0ai/server/
├── data/                    # 数据持久化目录
│   ├── mem0/               # Mem0核心数据
│   ├── qdrant/             # Qdrant向量数据
│   ├── neo4j/              # Neo4j图数据
│   └── vector_store/       # 向量存储
├── logs/                   # 应用日志
├── config/                 # 配置文件
├── scripts/                # 运维脚本
│   ├── backup-data.sh      # 数据备份
│   ├── restore-data.sh     # 数据恢复
│   ├── verify-data.sh      # 数据验证
│   └── fix-permissions.sh  # 权限修复
├── docker-compose.yaml     # 主配置（推荐）
├── docker-compose.dev.yaml # 开发环境
├── docker-compose.prod.yaml# 生产环境
└── .env.template          # 环境变量模板
```

### 容器内路径映射
```
容器路径          主机路径               用途
/app/src      -> ./server            源代码（只读）
/app/data     -> ./data              数据持久化（读写）
/app/logs     -> ./logs              日志文件（读写）
/app/config   -> ./config            配置文件（读写）
```

## 🚀 快速开始

### 1. 环境准备
```bash
# 克隆项目
cd /opt/mem0ai/server

# 复制环境变量模板
cp .env.template .env

# 编辑环境变量（必需）
nano .env
# 至少设置：
# OPENAI_API_KEY=your_api_key_here
# PUID=1000  # 你的用户ID
# PGID=1000  # 你的组ID
```

### 2. 数据备份（如有现有数据）
```bash
# 备份现有数据
./scripts/backup-data.sh

# 验证备份
ls -la backups/
```

### 3. 启动服务
```bash
# 开发环境（推荐）
docker-compose -f docker-compose.dev.yaml up -d

# 或使用主配置
docker-compose -f docker-compose.new.yaml up -d

# 检查服务状态
docker-compose ps
```

### 4. 验证部署
```bash
# 运行完整验证
./scripts/verify-data.sh

# 检查API健康
curl http://localhost:8000/health

# 查看日志
docker-compose logs -f mem0-api
```

## 📚 详细使用指南

### 开发环境部署

#### 特性
- 代码热重载
- 详细日志输出
- 开发工具集成
- 主机目录挂载便于调试

#### 启动命令
```bash
# 使用开发配置
docker-compose -f docker-compose.dev.yaml up -d

# 包含数据库管理工具
docker-compose -f docker-compose.dev.yaml --profile tools up -d

# 查看实时日志
docker-compose -f docker-compose.dev.yaml logs -f
```

#### 开发工具访问
- **API文档**: http://localhost:8000/docs
- **Adminer**: http://localhost:8080 (需要 --profile tools)
- **Neo4j浏览器**: http://localhost:7474
- **Qdrant仪表板**: http://localhost:6333/dashboard

### 生产环境部署

#### 特性
- 优化的性能配置
- 安全增强
- 资源限制
- 只读文件系统
- 多阶段构建

#### 准备步骤
```bash
# 1. 创建生产环境配置
cp .env.template .env.prod

# 2. 编辑生产配置
nano .env.prod
# 设置：
# ENVIRONMENT=production
# DEBUG=false
# LOG_LEVEL=INFO
# API_WORKERS=4

# 3. 创建密钥文件目录
mkdir -p secrets

# 4. 创建密钥文件
echo "your_neo4j_password" > secrets/neo4j_password.txt
echo "your_openai_api_key" > secrets/openai_api_key.txt
chmod 600 secrets/*
```

#### 启动生产环境
```bash
# 构建生产镜像
docker-compose -f docker-compose.prod.yaml build

# 启动生产服务
docker-compose -f docker-compose.prod.yaml up -d

# 包含Nginx反向代理
docker-compose -f docker-compose.prod.yaml --profile nginx up -d

# 包含监控服务
docker-compose -f docker-compose.prod.yaml --profile monitoring up -d
```

## 🔧 运维操作

### 数据备份与恢复

#### 自动备份
```bash
# 创建备份
./scripts/backup-data.sh

# 自定义备份目录
./scripts/backup-data.sh -d /backup/mem0

# 设置保留天数
BACKUP_RETENTION_DAYS=30 ./scripts/backup-data.sh
```

#### 数据恢复
```bash
# 列出可用备份
./scripts/restore-data.sh --list

# 预览恢复过程
./scripts/restore-data.sh mem0_backup_20241201_143000 --dry-run

# 执行恢复
./scripts/restore-data.sh mem0_backup_20241201_143000

# 跳过确认
./scripts/restore-data.sh mem0_backup_20241201_143000 -y
```

### 数据验证与健康检查

#### 完整验证
```bash
# 完整的系统验证
./scripts/verify-data.sh

# 快速健康检查
./scripts/verify-data.sh --quick

# 详细诊断
./scripts/verify-data.sh --detailed

# 修复权限问题
./scripts/verify-data.sh --fix-permissions
```

#### 权限管理
```bash
# 检查权限问题
./scripts/fix-permissions.sh --dry-run

# 修复权限
./scripts/fix-permissions.sh

# 指定用户ID
./scripts/fix-permissions.sh -u 1000 -g 1000
```

### 服务管理

#### 日常操作
```bash
# 查看服务状态
docker-compose ps

# 查看日志
docker-compose logs -f mem0-api

# 重启服务
docker-compose restart mem0-api

# 更新服务
docker-compose pull
docker-compose up -d
```

#### 扩容操作
```bash
# 扩展API服务
docker-compose up -d --scale mem0-api=3

# 查看资源使用
docker stats
```

## 🔄 迁移指南

### 从旧配置迁移

#### 安全迁移步骤
```bash
# 1. 备份现有数据
./scripts/backup-data.sh

# 2. 停止现有服务
docker-compose down

# 3. 备份当前配置
cp docker-compose.yaml docker-compose.yaml.backup

# 4. 使用新配置
cp docker-compose.new.yaml docker-compose.yaml

# 5. 启动新服务
docker-compose up -d

# 6. 验证迁移结果
./scripts/verify-data.sh
```

#### 回滚操作
```bash
# 如果出现问题，快速回滚
docker-compose down
cp docker-compose.yaml.backup docker-compose.yaml
docker-compose up -d

# 恢复数据（如果需要）
./scripts/restore-data.sh [backup_name]
```

## 🔍 故障排查

### 常见问题

#### 1. 权限问题
**症状**: 容器启动失败，权限被拒绝
```bash
# 诊断
./scripts/fix-permissions.sh --dry-run

# 解决
./scripts/fix-permissions.sh
sudo chown -R $USER:$USER ./data ./logs ./config
```

#### 2. 数据库连接失败
**症状**: API健康检查失败
```bash
# 检查网络
docker network ls
docker network inspect mem0-network

# 检查服务
docker-compose ps
docker-compose logs qdrant
docker-compose logs neo4j
```

#### 3. 磁盘空间不足
**症状**: 写入失败，性能下降
```bash
# 检查磁盘使用
df -h
./scripts/verify-data.sh --detailed

# 清理
docker system prune -f
docker volume prune -f
```

#### 4. 内存不足
**症状**: 服务重启，OOM错误
```bash
# 检查内存使用
docker stats
free -h

# 调整资源限制
# 编辑 docker-compose.yaml 中的 deploy.resources
```

### 日志分析

#### 关键日志位置
```bash
# 应用日志
docker-compose logs mem0-api

# 数据库日志
docker-compose logs qdrant
docker-compose logs neo4j

# 系统日志
./logs/validation_report_*.json
```

#### 日志级别调整
```bash
# 开发环境 - 详细日志
export LOG_LEVEL=DEBUG

# 生产环境 - 精简日志
export LOG_LEVEL=WARNING
```

## 📊 监控与告警

### 健康检查端点

#### API健康检查
```bash
# 基础健康检查
curl http://localhost:8000/health

# 返回示例
{
  "status": "healthy",
  "service": "mem0-api",
  "version": "1.0.0",
  "timestamp": **********.89,
  "checks": {
    "memory_instance": "ok",
    "vector_store": "ok", 
    "graph_store": "ok",
    "data_integrity": "ok"
  }
}
```

#### 数据库健康检查
```bash
# Qdrant
curl http://localhost:6333/health

# Neo4j
curl http://localhost:7474/
```

### 监控指标

#### 系统指标
- CPU使用率
- 内存使用率  
- 磁盘空间
- 网络I/O
- 容器状态

#### 应用指标
- API响应时间
- 请求成功率
- 数据库连接数
- 内存条目数量
- 错误率

## 🔐 安全最佳实践

### 1. 网络安全
```yaml
# 仅本地访问数据库
ports:
  - "127.0.0.1:6333:6333"  # Qdrant
  - "127.0.0.1:7474:7474"  # Neo4j
```

### 2. 文件权限
```bash
# 限制敏感文件权限
chmod 600 .env secrets/*
chmod 700 scripts/
```

### 3. 容器安全
```yaml
# 生产环境安全配置
security_opt:
  - no-new-privileges:true
read_only: true
user: "1000:1000"
```

### 4. 密钥管理
```bash
# 使用Docker secrets
secrets:
  openai_api_key:
    file: ./secrets/openai_api_key.txt
```

## 📈 性能优化

### 1. 资源配置
```yaml
# 根据负载调整资源限制
deploy:
  resources:
    limits:
      memory: 4G
      cpus: '2.0'
    reservations:
      memory: 2G
      cpus: '1.0'
```

### 2. 数据库优化
```yaml
# Neo4j内存配置
environment:
  - NEO4J_dbms_memory_heap_max__size=8G
  - NEO4J_dbms_memory_pagecache_size=4G

# Qdrant性能配置
environment:
  - QDRANT__STORAGE__PERFORMANCE__MAX_SEARCH_THREADS=4
```

### 3. API服务优化
```bash
# 使用多worker配置
export API_WORKERS=4
export MAX_REQUESTS=1000
```

## 🆘 支持与联系

### 文档资源
- [项目README](../README.md)
- [API文档](http://localhost:8000/docs)
- [CLAUDE.md指南](../CLAUDE.md)

### 问题报告
如果遇到问题，请提供以下信息：
1. 系统环境信息
2. 错误日志
3. 配置文件
4. 验证报告输出

### 获取帮助
```bash
# 运行诊断
./scripts/verify-data.sh --detailed > diagnosis.txt

# 收集日志
docker-compose logs > service.log

# 生成报告
tar -czf mem0-support-$(date +%Y%m%d).tar.gz \
  diagnosis.txt service.log .env docker-compose.yaml
```

---

## 🎉 部署完成

恭喜！您已经成功部署了全新的 Mem0 数据持久化架构。新架构提供了：

- 🔒 **数据安全保障**：完整的备份恢复机制
- ⚡ **高性能运行**：优化的资源配置
- 🛠️ **运维友好**：自动化的管理脚本
- 🔧 **故障自愈**：智能的权限修复和数据验证
- 📊 **全面监控**：详细的健康检查和日志记录

享受您的新 Mem0 部署吧！ 🚀