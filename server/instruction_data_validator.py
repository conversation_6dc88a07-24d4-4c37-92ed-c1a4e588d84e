#!/usr/bin/env python3
"""
指令管理数据验证器
用于验证和修复指令模板和分类数据的完整性
"""

import json
import re
import logging
from datetime import datetime
from typing import Dict, List, Any, Optional, Tuple
from pathlib import Path

logger = logging.getLogger(__name__)


class InstructionTemplate:
    """指令模板数据类"""
    
    def __init__(self, data: Dict[str, Any]):
        self.id = data.get('id', '')
        self.name = data.get('name', '')
        self.description = data.get('description', '')
        self.content = data.get('content', '')
        self.category = data.get('category', '')
        self.tags = data.get('tags', [])
        self.is_active = data.get('isActive', True)
        self.created_at = data.get('createdAt', '')
        self.updated_at = data.get('updatedAt', '')
    
    def to_dict(self) -> Dict[str, Any]:
        return {
            'id': self.id,
            'name': self.name,
            'description': self.description,
            'content': self.content,
            'category': self.category,
            'tags': self.tags,
            'isActive': self.is_active,
            'createdAt': self.created_at,
            'updatedAt': self.updated_at
        }


class InstructionCategory:
    """指令分类数据类"""
    
    def __init__(self, data: Dict[str, Any]):
        self.id = data.get('id', '')
        self.name = data.get('name', '')
        self.description = data.get('description', '')
        self.color = data.get('color', '#000000')
    
    def to_dict(self) -> Dict[str, Any]:
        return {
            'id': self.id,
            'name': self.name,
            'description': self.description,
            'color': self.color
        }


class InstructionDataValidator:
    """指令数据验证器"""
    
    def __init__(self, templates: List[Dict[str, Any]], categories: List[Dict[str, Any]]):
        self.templates = [InstructionTemplate(t) for t in templates]
        self.categories = [InstructionCategory(c) for c in categories]
        self.validation_errors = []
        self.validation_warnings = []
    
    def validate_all(self) -> Dict[str, Any]:
        """执行完整的数据验证"""
        self.validation_errors = []
        self.validation_warnings = []
        
        logger.info("开始指令管理数据验证")
        
        # 验证模板
        for i, template in enumerate(self.templates):
            try:
                self._validate_template(template)
            except ValueError as e:
                self.validation_errors.append(f"模板 {i + 1}: {str(e)}")
        
        # 验证分类
        for i, category in enumerate(self.categories):
            try:
                self._validate_category(category)
            except ValueError as e:
                self.validation_errors.append(f"分类 {i + 1}: {str(e)}")
        
        # 验证引用完整性
        reference_errors = self._validate_references()
        self.validation_errors.extend(reference_errors)
        
        # 检查重复数据
        duplicate_warnings = self._check_duplicates()
        self.validation_warnings.extend(duplicate_warnings)
        
        return {
            'is_valid': len(self.validation_errors) == 0,
            'errors': self.validation_errors,
            'warnings': self.validation_warnings,
            'summary': {
                'total_templates': len(self.templates),
                'total_categories': len(self.categories),
                'error_count': len(self.validation_errors),
                'warning_count': len(self.validation_warnings)
            }
        }
    
    def _validate_template(self, template: InstructionTemplate) -> None:
        """验证单个模板"""
        # 验证ID
        if not template.id or not isinstance(template.id, str):
            raise ValueError("模板ID无效")
        
        # 验证名称
        if not template.name or not template.name.strip():
            raise ValueError("模板名称不能为空")
        
        if len(template.name) < 2:
            raise ValueError("模板名称至少需要2个字符")
        
        if len(template.name) > 100:
            raise ValueError("模板名称不能超过100个字符")
        
        # 验证名称格式
        if not re.match(r'^[a-zA-Z0-9\u4e00-\u9fa5\s\-_]+$', template.name):
            raise ValueError("模板名称只能包含字母、数字、中文、空格、连字符和下划线")
        
        # 验证描述
        if template.description and len(template.description) > 500:
            raise ValueError("描述不能超过500个字符")
        
        # 验证内容
        if not template.content or not template.content.strip():
            raise ValueError("指令内容不能为空")
        
        if len(template.content) < 10:
            raise ValueError("指令内容至少需要10个字符")
        
        if len(template.content) > 5000:
            raise ValueError("指令内容不能超过5000个字符")
        
        # 验证分类
        if not template.category:
            raise ValueError("请选择一个分类")
        
        # 验证标签
        if template.tags:
            if len(template.tags) > 10:
                raise ValueError("标签数量不能超过10个")
            
            for tag in template.tags:
                if len(tag) > 20:
                    raise ValueError("每个标签长度不能超过20个字符")
                
                if not re.match(r'^[a-zA-Z0-9\u4e00-\u9fa5\-_]+$', tag):
                    raise ValueError("标签只能包含字母、数字、中文、连字符和下划线")
        
        # 验证变量占位符
        self._validate_variables(template.content)
        
        # 验证时间戳
        if template.created_at:
            try:
                datetime.fromisoformat(template.created_at.replace('Z', '+00:00'))
            except ValueError:
                raise ValueError("创建时间格式无效")
        
        if template.updated_at:
            try:
                datetime.fromisoformat(template.updated_at.replace('Z', '+00:00'))
            except ValueError:
                raise ValueError("更新时间格式无效")
    
    def _validate_category(self, category: InstructionCategory) -> None:
        """验证单个分类"""
        # 验证ID
        if not category.id or not isinstance(category.id, str):
            raise ValueError("分类ID无效")
        
        # 验证名称
        if not category.name or not category.name.strip():
            raise ValueError("分类名称不能为空")
        
        if len(category.name) > 50:
            raise ValueError("分类名称不能超过50个字符")
        
        # 验证描述
        if category.description and len(category.description) > 200:
            raise ValueError("分类描述不能超过200个字符")
        
        # 验证颜色格式
        if not re.match(r'^#[0-9a-fA-F]{6}$', category.color):
            raise ValueError("颜色格式无效，应为#RRGGBB格式")
    
    def _validate_variables(self, content: str) -> None:
        """验证变量占位符"""
        variable_pattern = re.compile(r'\{\{([^}]+)\}\}')
        matches = variable_pattern.findall(content)
        
        for var_name in matches:
            var_name = var_name.strip()
            if not re.match(r'^[a-zA-Z_][a-zA-Z0-9_]*$', var_name):
                raise ValueError(f'变量名 "{var_name}" 无效。变量名只能包含字母、数字和下划线，且不能以数字开头')
    
    def _validate_references(self) -> List[str]:
        """验证引用完整性"""
        errors = []
        category_ids = {c.id for c in self.categories}
        
        for i, template in enumerate(self.templates):
            if template.category not in category_ids:
                errors.append(f"模板 {i + 1} 引用了不存在的分类: {template.category}")
        
        return errors
    
    def _check_duplicates(self) -> List[str]:
        """检查重复数据"""
        warnings = []
        
        # 检查模板名称重复
        template_names = {}
        for i, template in enumerate(self.templates):
            name = template.name.lower().strip()
            if name in template_names:
                warnings.append(f'模板名称重复: "{template.name}" (位置 {template_names[name] + 1} 和 {i + 1})')
            else:
                template_names[name] = i
        
        # 检查分类名称重复
        category_names = {}
        for i, category in enumerate(self.categories):
            name = category.name.lower().strip()
            if name in category_names:
                warnings.append(f'分类名称重复: "{category.name}" (位置 {category_names[name] + 1} 和 {i + 1})')
            else:
                category_names[name] = i
        
        return warnings
    
    def fix_common_issues(self) -> Dict[str, Any]:
        """修复常见问题"""
        fixes = []
        
        # 修复模板问题
        for i, template in enumerate(self.templates):
            # 修复缺失的时间戳
            if not template.created_at:
                template.created_at = datetime.now().isoformat() + 'Z'
                fixes.append(f"为模板 {i + 1} 添加了创建时间")
            
            if not template.updated_at:
                template.updated_at = datetime.now().isoformat() + 'Z'
                fixes.append(f"为模板 {i + 1} 添加了更新时间")
            
            # 清理标签格式
            if template.tags:
                original_count = len(template.tags)
                template.tags = [tag.strip() for tag in template.tags if tag.strip()]
                if len(template.tags) != original_count:
                    fixes.append(f"清理了模板 {i + 1} 的标签格式")
            
            # 清理内容格式
            if template.content:
                original_content = template.content
                template.content = template.content.strip()
                if template.content != original_content:
                    fixes.append(f"清理了模板 {i + 1} 的内容格式")
            
            if template.description:
                original_desc = template.description
                template.description = template.description.strip()
                if template.description != original_desc:
                    fixes.append(f"清理了模板 {i + 1} 的描述格式")
        
        return {
            'fixed_templates': [t.to_dict() for t in self.templates],
            'fixed_categories': [c.to_dict() for c in self.categories],
            'fixes': fixes,
            'timestamp': datetime.now().isoformat()
        }
    
    def save_validation_report(self, output_path: Optional[str] = None) -> str:
        """保存验证报告"""
        if output_path is None:
            output_path = f"instruction_validation_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        
        validation_result = self.validate_all()
        
        report = {
            'timestamp': datetime.now().isoformat(),
            'validation_result': validation_result,
            'templates_count': len(self.templates),
            'categories_count': len(self.categories)
        }
        
        output_path = Path(output_path)
        output_path.parent.mkdir(parents=True, exist_ok=True)
        
        with open(output_path, 'w', encoding='utf-8') as f:
            json.dump(report, f, indent=2, ensure_ascii=False)
        
        logger.info(f"指令验证报告已保存到: {output_path}")
        return str(output_path)


def validate_instruction_data_from_file(config_file: str) -> Dict[str, Any]:
    """从配置文件验证指令数据"""
    try:
        with open(config_file, 'r', encoding='utf-8') as f:
            config = json.load(f)
        
        templates = config.get('instruction_templates', [])
        categories = config.get('instruction_categories', [])
        
        validator = InstructionDataValidator(templates, categories)
        return validator.validate_all()
        
    except Exception as e:
        logger.error(f"验证指令数据时出错: {e}")
        return {
            'is_valid': False,
            'errors': [f"文件读取错误: {str(e)}"],
            'warnings': [],
            'summary': {
                'total_templates': 0,
                'total_categories': 0,
                'error_count': 1,
                'warning_count': 0
            }
        }


if __name__ == "__main__":
    import argparse
    
    parser = argparse.ArgumentParser(description="指令管理数据验证器")
    parser.add_argument("--config", help="配置文件路径")
    parser.add_argument("--templates", help="模板JSON文件路径")
    parser.add_argument("--categories", help="分类JSON文件路径")
    parser.add_argument("--output", help="输出报告路径")
    parser.add_argument("--fix", action="store_true", help="尝试修复问题")
    
    args = parser.parse_args()
    
    validator = None

    if args.config:
        result = validate_instruction_data_from_file(args.config)
        # 为了保存报告，我们需要创建一个validator实例
        try:
            with open(args.config, 'r', encoding='utf-8') as f:
                config = json.load(f)
            templates = config.get('instruction_templates', [])
            categories = config.get('instruction_categories', [])
            validator = InstructionDataValidator(templates, categories)
        except Exception:
            pass
    else:
        # 示例数据
        templates = [
            {
                'id': 'template_1',
                'name': '测试模板',
                'description': '这是一个测试模板',
                'content': '请根据 {{input}} 生成相应的回答',
                'category': 'general',
                'tags': ['测试', '示例'],
                'isActive': True,
                'createdAt': '2024-01-01T00:00:00.000Z',
                'updatedAt': '2024-01-01T00:00:00.000Z'
            }
        ]

        categories = [
            {
                'id': 'general',
                'name': '通用指令',
                'description': '适用于各种场景的通用指令模板',
                'color': '#00d4aa'
            }
        ]

        validator = InstructionDataValidator(templates, categories)
        result = validator.validate_all()
    
    print(f"验证结果: {'通过' if result['is_valid'] else '失败'}")
    print(f"错误数量: {result['summary']['error_count']}")
    print(f"警告数量: {result['summary']['warning_count']}")
    
    if result['errors']:
        print("\n错误:")
        for error in result['errors']:
            print(f"  - {error}")
    
    if result['warnings']:
        print("\n警告:")
        for warning in result['warnings']:
            print(f"  - {warning}")
    
    if args.output and validator:
        validator.save_validation_report(args.output)
