# =============================================================================
# Mem0 简化的开发环境 Dockerfile
# 参考简化的主 Dockerfile，避免复杂权限处理
# =============================================================================

# 基础镜像
FROM python:3.11-slim

# 配置 Debian 国内镜像源以加速包下载
RUN sed -i 's/deb.debian.org/mirrors.tuna.tsinghua.edu.cn/g' /etc/apt/sources.list.d/debian.sources || true

# 设置时区
ENV TZ=Asia/Shanghai
RUN ln -snf /usr/share/zoneinfo/$TZ /etc/localtime && echo $TZ > /etc/timezone

# 设置 Python 环境优化
ENV PYTHONUNBUFFERED=1
ENV PYTHONDONTWRITEBYTECODE=1
ENV PIP_NO_CACHE_DIR=1
ENV PIP_DISABLE_PIP_VERSION_CHECK=1
ENV PYTHONWARNINGS=ignore::DeprecationWarning,ignore::UserWarning

# 安装系统依赖（包含开发工具）
RUN apt-get update && apt-get install -y \
    curl \
    build-essential \
    netcat-traditional \
    git \
    vim \
    htop \
    && rm -rf /var/lib/apt/lists/*

# 设置工作目录
WORKDIR /app

# 创建必要的目录结构
RUN mkdir -p /app/data /app/logs /app/config /app/src /app/packages

# 复制 requirements 文件
COPY server/requirements.txt /app/

# 配置 pip 使用国内镜像源加速下载
RUN pip config set global.index-url https://pypi.tuna.tsinghua.edu.cn/simple && \
    pip config set global.trusted-host pypi.tuna.tsinghua.edu.cn

# 安装 Python 依赖
RUN pip install --upgrade pip && \
    pip install --no-cache-dir -r requirements.txt

# 设置环境变量
ENV PYTHONPATH=/app/src:/app/packages

# 复制权限修复入口点
COPY server/entrypoint.sh /app/entrypoint.sh
RUN chmod +x /app/entrypoint.sh

# 暴露端口
EXPOSE 8000

# 健康检查（简化版本）
HEALTHCHECK --interval=30s --timeout=10s --start-period=60s --retries=3 \
    CMD python -c "import urllib.request; urllib.request.urlopen('http://localhost:8000/health', timeout=5)" || exit 1

# 入口点
ENTRYPOINT ["/app/entrypoint.sh"]

# 默认命令（支持热重载）
CMD ["uvicorn", "main:app", "--host", "0.0.0.0", "--port", "8000", "--reload"]