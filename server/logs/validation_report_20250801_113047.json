{"timestamp": "2025-08-01T11:29:06.863590", "data_path": "/app/data", "history_db_path": "/app/data/history.db", "validations": {"directory_structure": {"status": "passed", "total_checks": 5, "passed_checks": 5, "failed_checks": 0, "warnings": 0, "details": {"data": {"status": "exists", "readable": true, "writable": true, "size": 679913288}, "data/mem0": {"status": "exists", "readable": true, "writable": true, "size": 614457}, "data/vector_store": {"status": "exists", "readable": true, "writable": true, "size": 0}, "logs": {"status": "exists", "readable": true, "writable": true, "size": 0}, "config": {"status": "exists", "readable": true, "writable": true, "size": 0}}}, "file_permissions": {"status": "passed", "total_checks": 40, "passed_checks": 40, "failed_checks": 0, "warnings": 0, "details": {"data/history.db": {"permissions": "644", "readable": true, "writable": true, "size": 12288, "owner": 0, "group": 0, "status": "ok"}, "data/neo4j/data/databases/neo4j/neostore.relationshiptypestore.db": {"permissions": "644", "readable": true, "writable": true, "size": 0, "owner": 7474, "group": 7474, "status": "ok"}, "data/neo4j/data/databases/neo4j/neostore.relationshipgroupstore.degrees.db": {"permissions": "644", "readable": true, "writable": true, "size": 40960, "owner": 7474, "group": 7474, "status": "ok"}, "data/neo4j/data/databases/neo4j/neostore.relationshipgroupstore.db": {"permissions": "644", "readable": true, "writable": true, "size": 8192, "owner": 7474, "group": 7474, "status": "ok"}, "data/neo4j/data/databases/neo4j/neostore.propertystore.db": {"permissions": "644", "readable": true, "writable": true, "size": 8192, "owner": 7474, "group": 7474, "status": "ok"}, "data/neo4j/data/databases/neo4j/neostore.nodestore.db": {"permissions": "644", "readable": true, "writable": true, "size": 0, "owner": 7474, "group": 7474, "status": "ok"}, "data/neo4j/data/databases/neo4j/neostore.labeltokenstore.db": {"permissions": "644", "readable": true, "writable": true, "size": 0, "owner": 7474, "group": 7474, "status": "ok"}, "data/neo4j/data/databases/neo4j/neostore.relationshipstore.db": {"permissions": "644", "readable": true, "writable": true, "size": 0, "owner": 7474, "group": 7474, "status": "ok"}, "data/neo4j/data/databases/neo4j/neostore.counts.db": {"permissions": "644", "readable": true, "writable": true, "size": 40960, "owner": 7474, "group": 7474, "status": "ok"}, "data/neo4j/data/databases/neo4j/neostore.indexstats.db": {"permissions": "644", "readable": true, "writable": true, "size": 49152, "owner": 7474, "group": 7474, "status": "ok"}, "data/neo4j/data/databases/neo4j/neostore.schemastore.db": {"permissions": "644", "readable": true, "writable": true, "size": 8192, "owner": 7474, "group": 7474, "status": "ok"}, "data/neo4j/data/databases/system/neostore.relationshiptypestore.db": {"permissions": "644", "readable": true, "writable": true, "size": 8192, "owner": 7474, "group": 7474, "status": "ok"}, "data/neo4j/data/databases/system/neostore.relationshipgroupstore.degrees.db": {"permissions": "644", "readable": true, "writable": true, "size": 40960, "owner": 7474, "group": 7474, "status": "ok"}, "data/neo4j/data/databases/system/neostore.relationshipgroupstore.db": {"permissions": "644", "readable": true, "writable": true, "size": 8192, "owner": 7474, "group": 7474, "status": "ok"}, "data/neo4j/data/databases/system/neostore.propertystore.db": {"permissions": "644", "readable": true, "writable": true, "size": 8192, "owner": 7474, "group": 7474, "status": "ok"}, "data/neo4j/data/databases/system/neostore.nodestore.db": {"permissions": "644", "readable": true, "writable": true, "size": 8192, "owner": 7474, "group": 7474, "status": "ok"}, "data/neo4j/data/databases/system/neostore.labeltokenstore.db": {"permissions": "644", "readable": true, "writable": true, "size": 8192, "owner": 7474, "group": 7474, "status": "ok"}, "data/neo4j/data/databases/system/neostore.relationshipstore.db": {"permissions": "644", "readable": true, "writable": true, "size": 8192, "owner": 7474, "group": 7474, "status": "ok"}, "data/neo4j/data/databases/system/neostore.counts.db": {"permissions": "644", "readable": true, "writable": true, "size": 49152, "owner": 7474, "group": 7474, "status": "ok"}, "data/neo4j/data/databases/system/neostore.indexstats.db": {"permissions": "644", "readable": true, "writable": true, "size": 49152, "owner": 7474, "group": 7474, "status": "ok"}, "data/neo4j/data/databases/system/neostore.schemastore.db": {"permissions": "644", "readable": true, "writable": true, "size": 8192, "owner": 7474, "group": 7474, "status": "ok"}, "data/mem0/history.db": {"permissions": "644", "readable": true, "writable": true, "size": 614400, "owner": 1000, "group": 1000, "status": "ok"}, "data/mem0/config.json": {"permissions": "644", "readable": true, "writable": true, "size": 57, "owner": 0, "group": 0, "status": "ok"}, "data/qdrant/raft_state.json": {"permissions": "644", "readable": true, "writable": true, "size": 324, "owner": 1000, "group": 1000, "status": "ok"}, "data/qdrant/collections/mem0migrations/config.json": {"permissions": "644", "readable": true, "writable": true, "size": 595, "owner": 0, "group": 0, "status": "ok"}, "data/qdrant/collections/mem0migrations/0/shard_config.json": {"permissions": "644", "readable": true, "writable": true, "size": 21, "owner": 0, "group": 0, "status": "ok"}, "data/qdrant/collections/mem0migrations/0/replica_state.json": {"permissions": "644", "readable": true, "writable": true, "size": 87, "owner": 0, "group": 0, "status": "ok"}, "data/qdrant/collections/mem0migrations/0/segments/fbef2ca0-cde9-4ba1-bd2d-ecff7d2c6deb/segment.json": {"permissions": "644", "readable": true, "writable": true, "size": 211, "owner": 0, "group": 0, "status": "ok"}, "data/qdrant/collections/mem0migrations/0/segments/fbef2ca0-cde9-4ba1-bd2d-ecff7d2c6deb/payload_index/config.json": {"permissions": "644", "readable": true, "writable": true, "size": 21, "owner": 0, "group": 0, "status": "ok"}, "data/qdrant/collections/mem0migrations/0/segments/27fd28f2-9134-4ed6-b5c2-964fe488c470/segment.json": {"permissions": "644", "readable": true, "writable": true, "size": 211, "owner": 0, "group": 0, "status": "ok"}, "data/qdrant/collections/mem0migrations/0/segments/27fd28f2-9134-4ed6-b5c2-964fe488c470/payload_index/config.json": {"permissions": "644", "readable": true, "writable": true, "size": 21, "owner": 0, "group": 0, "status": "ok"}, "data/qdrant/collections/mem0/config.json": {"permissions": "644", "readable": true, "writable": true, "size": 595, "owner": 0, "group": 0, "status": "ok"}, "data/qdrant/collections/mem0/0/shard_config.json": {"permissions": "644", "readable": true, "writable": true, "size": 21, "owner": 0, "group": 0, "status": "ok"}, "data/qdrant/collections/mem0/0/replica_state.json": {"permissions": "644", "readable": true, "writable": true, "size": 87, "owner": 0, "group": 0, "status": "ok"}, "data/qdrant/collections/mem0/0/segments/0c9f6b55-6a0a-4f07-a9f6-88453f1e0b72/segment.json": {"permissions": "644", "readable": true, "writable": true, "size": 211, "owner": 0, "group": 0, "status": "ok"}, "data/qdrant/collections/mem0/0/segments/0c9f6b55-6a0a-4f07-a9f6-88453f1e0b72/payload_index/config.json": {"permissions": "644", "readable": true, "writable": true, "size": 21, "owner": 0, "group": 0, "status": "ok"}, "data/qdrant/collections/mem0/0/segments/0ed50f02-1356-41ba-9171-df8a8d140485/segment.json": {"permissions": "644", "readable": true, "writable": true, "size": 211, "owner": 0, "group": 0, "status": "ok"}, "data/qdrant/collections/mem0/0/segments/0ed50f02-1356-41ba-9171-df8a8d140485/payload_index/config.json": {"permissions": "644", "readable": true, "writable": true, "size": 21, "owner": 0, "group": 0, "status": "ok"}, "data/qdrant/aliases/data.json": {"permissions": "644", "readable": true, "writable": true, "size": 2, "owner": 1000, "group": 1000, "status": "ok"}}}, "database_integrity": {"status": "failed", "total_checks": 22, "passed_checks": 2, "failed_checks": 20, "warnings": 0, "details": {"history_database": {"status": "healthy", "integrity_check": "ok", "tables": ["history"], "table_stats": {"history": 0}, "size_bytes": 12288, "size_mb": 0.01}, "neo4j/data/databases/neo4j/neostore.relationshiptypestore.db": {"status": "error", "error": "database is locked"}, "neo4j/data/databases/neo4j/neostore.relationshipgroupstore.degrees.db": {"status": "error", "error": "database is locked"}, "neo4j/data/databases/neo4j/neostore.relationshipgroupstore.db": {"status": "error", "error": "database is locked"}, "neo4j/data/databases/neo4j/neostore.propertystore.db": {"status": "error", "error": "database is locked"}, "neo4j/data/databases/neo4j/neostore.nodestore.db": {"status": "error", "error": "database is locked"}, "neo4j/data/databases/neo4j/neostore.labeltokenstore.db": {"status": "error", "error": "database is locked"}, "neo4j/data/databases/neo4j/neostore.relationshipstore.db": {"status": "error", "error": "database is locked"}, "neo4j/data/databases/neo4j/neostore.counts.db": {"status": "error", "error": "database is locked"}, "neo4j/data/databases/neo4j/neostore.indexstats.db": {"status": "error", "error": "database is locked"}, "neo4j/data/databases/neo4j/neostore.schemastore.db": {"status": "error", "error": "database is locked"}, "neo4j/data/databases/system/neostore.relationshiptypestore.db": {"status": "error", "error": "database is locked"}, "neo4j/data/databases/system/neostore.relationshipgroupstore.degrees.db": {"status": "error", "error": "database is locked"}, "neo4j/data/databases/system/neostore.relationshipgroupstore.db": {"status": "error", "error": "database is locked"}, "neo4j/data/databases/system/neostore.propertystore.db": {"status": "error", "error": "database is locked"}, "neo4j/data/databases/system/neostore.nodestore.db": {"status": "error", "error": "database is locked"}, "neo4j/data/databases/system/neostore.labeltokenstore.db": {"status": "error", "error": "database is locked"}, "neo4j/data/databases/system/neostore.relationshipstore.db": {"status": "error", "error": "database is locked"}, "neo4j/data/databases/system/neostore.counts.db": {"status": "error", "error": "database is locked"}, "neo4j/data/databases/system/neostore.indexstats.db": {"status": "error", "error": "database is locked"}, "neo4j/data/databases/system/neostore.schemastore.db": {"status": "error", "error": "database is locked"}, "mem0/history.db": {"status": "healthy", "integrity_check": "ok", "tables": ["history", "category_cache"], "table_stats": {"history": 2547, "category_cache": 2}, "size_bytes": 614400, "size_mb": 0.59}}}, "data_consistency": {"status": "passed", "total_checks": 26, "passed_checks": 26, "failed_checks": 0, "warnings": 0, "details": {"data_consistency": {"status": "consistent", "message": "No data consistency issues found"}, "config_mem0/config.json": {"status": "valid", "size": 57}, "config_qdrant/raft_state.json": {"status": "valid", "size": 324}, "config_qdrant/collections/mem0migrations/config.json": {"status": "valid", "size": 595}, "config_qdrant/collections/mem0migrations/0/newest_clocks.json": {"status": "valid", "size": 100}, "config_qdrant/collections/mem0migrations/0/shard_config.json": {"status": "valid", "size": 21}, "config_qdrant/collections/mem0migrations/0/replica_state.json": {"status": "valid", "size": 87}, "config_qdrant/collections/mem0migrations/0/segments/fbef2ca0-cde9-4ba1-bd2d-ecff7d2c6deb/segment.json": {"status": "valid", "size": 211}, "config_qdrant/collections/mem0migrations/0/segments/fbef2ca0-cde9-4ba1-bd2d-ecff7d2c6deb/payload_index/config.json": {"status": "valid", "size": 21}, "config_qdrant/collections/mem0migrations/0/segments/27fd28f2-9134-4ed6-b5c2-964fe488c470/segment.json": {"status": "valid", "size": 208}, "config_qdrant/collections/mem0migrations/0/segments/27fd28f2-9134-4ed6-b5c2-964fe488c470/payload_index/config.json": {"status": "valid", "size": 21}, "config_qdrant/collections/mem0/config.json": {"status": "valid", "size": 595}, "config_qdrant/collections/mem0/0/shard_config.json": {"status": "valid", "size": 21}, "config_qdrant/collections/mem0/0/replica_state.json": {"status": "valid", "size": 87}, "config_qdrant/collections/mem0/0/segments/0c9f6b55-6a0a-4f07-a9f6-88453f1e0b72/segment.json": {"status": "valid", "size": 211}, "config_qdrant/collections/mem0/0/segments/0c9f6b55-6a0a-4f07-a9f6-88453f1e0b72/payload_index/config.json": {"status": "valid", "size": 21}, "config_qdrant/collections/mem0/0/segments/0ed50f02-1356-41ba-9171-df8a8d140485/segment.json": {"status": "valid", "size": 211}, "config_qdrant/collections/mem0/0/segments/0ed50f02-1356-41ba-9171-df8a8d140485/payload_index/config.json": {"status": "valid", "size": 21}, "config_qdrant/aliases/data.json": {"status": "valid", "size": 2}}}, "storage_health": {"status": "passed", "total_checks": 3, "passed_checks": 3, "failed_checks": 0, "warnings": 0, "details": {"disk_space": {"total_gb": 78.19, "free_gb": 64.6, "used_gb": 13.59, "usage_percent": 17.38, "status": "healthy"}, "data_directory_size": {"status": "measured", "size_mb": 648.41, "size_gb": 0.63}, "io_performance": {"status": "measured", "write_time_ms": 0.12, "read_time_ms": 0.04, "performance": "good"}}}}, "summary": {"total_checks": 96, "passed_checks": 76, "failed_checks": 20, "warnings": 0}, "overall_status": "degraded"}