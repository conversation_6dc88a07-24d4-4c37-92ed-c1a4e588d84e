# 多阶段构建优化生产镜像
# 阶段 1: 构建依赖
FROM python:3.12-slim as builder

# 配置 Debian 国内镜像源以加速包下载
RUN sed -i 's/deb.debian.org/mirrors.ustc.edu.cn/g' /etc/apt/sources.list.d/debian.sources && \
    sed -i 's/security.debian.org/mirrors.ustc.edu.cn/g' /etc/apt/sources.list.d/debian.sources

# 安装构建 Python 包所需的系统依赖
RUN apt-get update && apt-get install -y \
    build-essential \
    gcc \
    && rm -rf /var/lib/apt/lists/*

# 设置 Python 环境
ENV PYTHONUNBUFFERED=1
ENV PYTHONDONTWRITEBYTECODE=1
ENV PIP_NO_CACHE_DIR=1
ENV PIP_DISABLE_PIP_VERSION_CHECK=1

# 创建虚拟环境
RUN python -m venv /opt/venv
ENV PATH="/opt/venv/bin:$PATH"

# 复制并安装 Python 依赖
COPY server/requirements.txt .
RUN pip install --upgrade pip && \
    pip install -r requirements.txt

# 阶段 2: 生产运行时
FROM python:3.12-slim as production

# 配置 Debian 国内镜像源以加速包下载
RUN sed -i 's/deb.debian.org/mirrors.ustc.edu.cn/g' /etc/apt/sources.list.d/debian.sources && \
    sed -i 's/security.debian.org/mirrors.ustc.edu.cn/g' /etc/apt/sources.list.d/debian.sources

# 安装gosu用于用户切换
RUN apt-get update && apt-get install -y gosu && rm -rf /var/lib/apt/lists/*

# 为安全性创建非 root 用户
RUN groupadd -r mem0 && useradd -r -g mem0 -s /bin/false mem0

# 设置环境变量
ENV PYTHONUNBUFFERED=1
ENV PYTHONDONTWRITEBYTECODE=1
ENV PYTHONWARNINGS=ignore::DeprecationWarning,ignore::UserWarning
ENV PATH="/opt/venv/bin:$PATH"

# 从构建阶段复制虚拟环境
COPY --from=builder /opt/venv /opt/venv

# 创建应用目录并设置所有权
WORKDIR /app

# 创建数据目录用于持久存储
RUN mkdir -p /app/data

# 复制entrypoint脚本
COPY server/entrypoint.sh /entrypoint.sh
RUN chmod +x /entrypoint.sh

# 复制应用代码
COPY --chown=mem0:mem0 . .

# 暴露端口
EXPOSE 8000

# 使用 Python 内置模块添加健康检查（无外部依赖）
HEALTHCHECK --interval=30s --timeout=10s --start-period=40s --retries=3 \
    CMD python -c "import urllib.request, sys; urllib.request.urlopen('http://localhost:8000/health', timeout=5); sys.exit(0)" || exit 1

# 默认环境变量（可被覆盖）
ENV MEM0_DATA_PATH=/app/data
ENV API_PORT=8000

# 使用entrypoint脚本启动
ENTRYPOINT ["/entrypoint.sh"]
CMD ["uvicorn", "main:app", "--host", "0.0.0.0", "--port", "8000"]
