#!/bin/bash

# 指令管理数据修复脚本
# 用于检测和修复指令模板和分类数据的完整性问题

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 配置
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(cd "$SCRIPT_DIR/../.." && pwd)"
DATA_DIR="${DATA_DIR:-$PROJECT_ROOT/data}"
CONFIG_DIR="${CONFIG_DIR:-$PROJECT_ROOT/config}"
LOGS_DIR="${LOGS_DIR:-$PROJECT_ROOT/server/logs}"
BACKUP_DIR="${BACKUP_DIR:-$PROJECT_ROOT/backups}"

# 创建必要的目录
mkdir -p "$LOGS_DIR" "$BACKUP_DIR"

# 日志文件
LOG_FILE="$LOGS_DIR/instruction_data_fix_$(date +%Y%m%d_%H%M%S).log"

# 日志函数
log() {
    echo "$(date '+%Y-%m-%d %H:%M:%S') - $1" | tee -a "$LOG_FILE"
}

log_info() {
    echo -e "${BLUE}ℹ️  $1${NC}" | tee -a "$LOG_FILE"
}

log_success() {
    echo -e "${GREEN}✅ $1${NC}" | tee -a "$LOG_FILE"
}

log_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}" | tee -a "$LOG_FILE"
}

log_error() {
    echo -e "${RED}❌ $1${NC}" | tee -a "$LOG_FILE"
}

# 显示帮助信息
show_help() {
    cat << EOF
指令管理数据修复脚本

用法: $0 [选项]

选项:
    --check-only        仅检查数据完整性，不进行修复
    --backup           在修复前创建备份
    --config FILE      指定配置文件路径
    --dry-run          预览修复操作，不实际执行
    --verbose          显示详细输出
    --help             显示此帮助信息

示例:
    $0 --check-only                    # 仅检查数据
    $0 --backup --verbose              # 备份并修复数据
    $0 --config /path/to/config.json   # 使用指定配置文件
    $0 --dry-run                       # 预览修复操作

EOF
}

# 解析命令行参数
CHECK_ONLY=false
CREATE_BACKUP=false
DRY_RUN=false
VERBOSE=false
CONFIG_FILE=""

while [[ $# -gt 0 ]]; do
    case $1 in
        --check-only)
            CHECK_ONLY=true
            shift
            ;;
        --backup)
            CREATE_BACKUP=true
            shift
            ;;
        --config)
            CONFIG_FILE="$2"
            shift 2
            ;;
        --dry-run)
            DRY_RUN=true
            shift
            ;;
        --verbose)
            VERBOSE=true
            shift
            ;;
        --help)
            show_help
            exit 0
            ;;
        *)
            log_error "未知选项: $1"
            show_help
            exit 1
            ;;
    esac
done

# 检查Python环境
check_python() {
    if ! command -v python3 &> /dev/null; then
        log_error "Python3 未安装"
        exit 1
    fi
    
    # 检查必要的Python模块
    if ! python3 -c "import json, re, datetime, pathlib" 2>/dev/null; then
        log_error "缺少必要的Python模块"
        exit 1
    fi
    
    log_success "Python环境检查通过"
}

# 查找配置文件
find_config_file() {
    if [[ -n "$CONFIG_FILE" ]]; then
        if [[ ! -f "$CONFIG_FILE" ]]; then
            log_error "指定的配置文件不存在: $CONFIG_FILE"
            exit 1
        fi
        echo "$CONFIG_FILE"
        return
    fi

    # 搜索可能的配置文件位置
    local possible_configs=(
        "$CONFIG_DIR/mem0_config.json"
        "$DATA_DIR/config.json"
        "$PROJECT_ROOT/mem0_ui/config/default.json"
        "$PROJECT_ROOT/.mem0/config.json"
    )

    for config in "${possible_configs[@]}"; do
        if [[ -f "$config" ]]; then
            echo "$config"
            return
        fi
    done

    # 返回空字符串表示未找到配置文件
    echo ""
}

# 创建备份
create_backup() {
    if [[ "$CREATE_BACKUP" == "true" ]]; then
        local backup_name="instruction_data_backup_$(date +%Y%m%d_%H%M%S)"
        local backup_path="$BACKUP_DIR/$backup_name"
        
        log_info "创建数据备份: $backup_path"
        
        mkdir -p "$backup_path"
        
        # 备份配置文件
        if [[ -n "$1" && -f "$1" ]]; then
            cp "$1" "$backup_path/config.json"
        fi
        
        # 备份相关数据目录
        if [[ -d "$DATA_DIR" ]]; then
            cp -r "$DATA_DIR" "$backup_path/data" 2>/dev/null || true
        fi
        
        if [[ -d "$CONFIG_DIR" ]]; then
            cp -r "$CONFIG_DIR" "$backup_path/config" 2>/dev/null || true
        fi
        
        log_success "备份已创建: $backup_path"
        echo "$backup_path"
    fi
}

# 运行数据验证
run_validation() {
    local config_file="$1"
    local validator_script="$PROJECT_ROOT/server/instruction_data_validator.py"
    
    log_info "开始数据验证..."
    
    if [[ ! -f "$validator_script" ]]; then
        log_error "验证器脚本不存在: $validator_script"
        exit 1
    fi
    
    local cmd="python3 '$validator_script'"
    
    if [[ -n "$config_file" ]]; then
        cmd="$cmd --config '$config_file'"
    fi
    
    # 注意：Python脚本暂不支持--verbose参数
    
    local output_file="$LOGS_DIR/validation_result_$(date +%Y%m%d_%H%M%S).json"
    cmd="$cmd --output '$output_file'"
    
    if [[ "$DRY_RUN" == "true" ]]; then
        log_info "预览模式 - 将执行的命令:"
        echo "$cmd"
        return 0
    fi
    
    # 执行验证
    if eval "$cmd"; then
        log_success "数据验证完成"
        
        if [[ -f "$output_file" ]]; then
            log_info "验证报告已保存: $output_file"
            
            # 显示验证结果摘要
            if command -v jq &> /dev/null; then
                local is_valid=$(jq -r '.validation_result.is_valid' "$output_file")
                local error_count=$(jq -r '.validation_result.summary.error_count' "$output_file")
                local warning_count=$(jq -r '.validation_result.summary.warning_count' "$output_file")
                
                if [[ "$is_valid" == "true" ]]; then
                    log_success "数据验证通过"
                else
                    log_warning "发现 $error_count 个错误和 $warning_count 个警告"
                fi
            fi
        fi
        
        return 0
    else
        log_error "数据验证失败"
        return 1
    fi
}

# 运行数据修复
run_fix() {
    local config_file="$1"
    local validator_script="$PROJECT_ROOT/server/instruction_data_validator.py"
    
    log_info "开始数据修复..."
    
    local cmd="python3 '$validator_script' --fix"
    
    if [[ -n "$config_file" ]]; then
        cmd="$cmd --config '$config_file'"
    fi
    
    local output_file="$LOGS_DIR/fix_result_$(date +%Y%m%d_%H%M%S).json"
    cmd="$cmd --output '$output_file'"
    
    if [[ "$DRY_RUN" == "true" ]]; then
        log_info "预览模式 - 将执行的修复命令:"
        echo "$cmd"
        return 0
    fi
    
    # 执行修复
    if eval "$cmd"; then
        log_success "数据修复完成"
        
        if [[ -f "$output_file" ]]; then
            log_info "修复报告已保存: $output_file"
        fi
        
        return 0
    else
        log_error "数据修复失败"
        return 1
    fi
}

# 验证修复结果
verify_fix() {
    local config_file="$1"
    
    log_info "验证修复结果..."
    
    if run_validation "$config_file"; then
        log_success "修复验证通过"
        return 0
    else
        log_error "修复验证失败"
        return 1
    fi
}

# 主函数
main() {
    log_info "开始指令管理数据修复流程"
    log_info "日志文件: $LOG_FILE"
    
    # 检查环境
    check_python
    
    # 查找配置文件
    local config_file
    config_file=$(find_config_file)

    if [[ -n "$config_file" ]]; then
        log_info "使用配置文件: $config_file"
    else
        log_warning "未找到配置文件，将使用默认示例数据"
    fi
    
    # 创建备份
    local backup_path
    backup_path=$(create_backup "$config_file")
    
    # 运行验证
    if ! run_validation "$config_file"; then
        if [[ "$CHECK_ONLY" == "true" ]]; then
            log_error "数据验证失败，请检查错误信息"
            exit 1
        fi
        
        log_warning "数据验证发现问题，将尝试修复"
        
        # 运行修复
        if run_fix "$config_file"; then
            # 验证修复结果
            verify_fix "$config_file"
        else
            log_error "数据修复失败"
            
            if [[ -n "$backup_path" ]]; then
                log_info "可以从备份恢复: $backup_path"
            fi
            
            exit 1
        fi
    else
        log_success "数据验证通过，无需修复"
    fi
    
    log_success "指令管理数据修复流程完成"
}

# 信号处理
trap 'log_error "脚本被中断"; exit 1' INT TERM

# 执行主函数
main "$@"
