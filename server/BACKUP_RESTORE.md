# Mem0 备份与恢复操作指南

## 📋 概述

本指南详细介绍 Mem0 系统的数据备份、恢复和维护操作，确保数据安全和系统可靠性。

## 🎯 数据类型说明

### 需要备份的数据
- **SQLite数据库**: `./data/mem0/history.db` - 历史记录和元数据
- **向量数据**: `./data/qdrant/` - Qdrant向量存储
- **图数据**: `./data/neo4j/` - Neo4j图数据库
- **配置文件**: Docker配置、环境变量等
- **应用数据**: `./data/vector_store/` - 其他应用数据

### 数据重要性级别
- 🔴 **关键数据**: SQLite数据库、Neo4j数据
- 🟡 **重要数据**: Qdrant向量数据、配置文件  
- 🟢 **一般数据**: 日志文件、临时文件

## 📦 备份操作

### 自动备份脚本使用

#### 基础备份
```bash
# 执行完整备份
./scripts/backup-data.sh

# 指定备份目录
./scripts/backup-data.sh -d /backup/mem0

# 指定compose文件
./scripts/backup-data.sh -f docker-compose.prod.yaml
```

#### 备份输出示例
```
[INFO] Starting Mem0 data backup process...
[INFO] Backup will be saved to: ./backups/mem0_backup_20241201_143000
[INFO] Created backup directory: ./backups/mem0_backup_20241201_143000
[INFO] Backing up host data directory...
[SUCCESS] Host data backup completed
[INFO] Backing up Docker volumes...
[INFO] Backing up volume: mem0-stack_qdrant-data
[SUCCESS] Volume mem0-stack_qdrant-data backup completed
[INFO] Backing up database states...
[SUCCESS] Database state backup completed
[INFO] Backing up configuration files...
[SUCCESS] Configuration backup completed
[INFO] Generating backup manifest...
[SUCCESS] Backup manifest generated
[INFO] Verifying backup integrity...
[SUCCESS] Backup verification completed successfully
[SUCCESS] ✅ Backup completed successfully!
[INFO] Backup location: ./backups/mem0_backup_20241201_143000
[INFO] Total size: 234MB
```

#### 备份内容结构
```
backups/mem0_backup_20241201_143000/
├── MANIFEST.txt                 # 备份清单和元信息
├── host_data.tar.gz            # 主机数据目录备份
├── volume_qdrant-data.tar.gz   # Qdrant向量数据
├── volume_neo4j-data.tar.gz    # Neo4j图数据
├── database_stats.json         # 数据库统计信息
└── configs/                    # 配置文件备份
    ├── docker-compose.yaml
    ├── .env
    └── main.py.backup
```

### 定期备份设置

#### 使用Cron定时备份
```bash
# 编辑crontab
crontab -e

# 添加定时任务（每日凌晨2点备份）
0 2 * * * cd /opt/mem0ai/server && ./scripts/backup-data.sh >/dev/null 2>&1

# 每周日凌晨1点备份到外部存储
0 1 * * 0 cd /opt/mem0ai/server && ./scripts/backup-data.sh -d /mnt/backup/mem0
```

#### 使用systemd定时器
```bash
# 创建备份服务
sudo tee /etc/systemd/system/mem0-backup.service << EOF
[Unit]
Description=Mem0 Data Backup
After=docker.service

[Service]
Type=oneshot
User=mem0
WorkingDirectory=/opt/mem0ai/server
ExecStart=/opt/mem0ai/server/scripts/backup-data.sh
StandardOutput=journal
StandardError=journal
EOF

# 创建定时器
sudo tee /etc/systemd/system/mem0-backup.timer << EOF
[Unit]
Description=Run Mem0 backup daily
Requires=mem0-backup.service

[Timer]
OnCalendar=daily
Persistent=true

[Install]
WantedBy=timers.target
EOF

# 启用并启动定时器
sudo systemctl enable mem0-backup.timer
sudo systemctl start mem0-backup.timer
```

### 高级备份配置

#### 环境变量配置
```bash
# 设置备份保留天数
export BACKUP_RETENTION_DAYS=30

# 设置备份目录
export BACKUP_DIR=/backup/mem0

# 执行备份
./scripts/backup-data.sh
```

#### 远程备份
```bash
# 备份到远程存储
./scripts/backup-data.sh -d /mnt/nfs/backup

# 备份后上传到云存储（示例）
#!/bin/bash
BACKUP_NAME=$(./scripts/backup-data.sh | grep "Backup location" | cut -d: -f2)
tar -czf "${BACKUP_NAME}.tar.gz" "$BACKUP_NAME"
aws s3 cp "${BACKUP_NAME}.tar.gz" s3://my-backup-bucket/mem0/
```

## 🔄 恢复操作

### 恢复前准备

#### 查看可用备份
```bash
# 列出所有备份
./scripts/restore-data.sh --list

# 输出示例
Available backups in ./backups:
  📦 mem0_backup_20241201_143000 (Size: 234MB, Date: 2024-12-01)
  📦 mem0_backup_20241130_143000 (Size: 189MB, Date: 2024-11-30)
  📦 mem0_backup_20241129_143000 (Size: 156MB, Date: 2024-11-29)
```

#### 验证备份完整性
```bash
# 验证特定备份
./scripts/restore-data.sh mem0_backup_20241201_143000 --dry-run

# 输出示例
Backup Information:
  📍 Location: ./backups/mem0_backup_20241201_143000
  📊 Size: 234MB
  📅 Created: 2024-12-01 14:30:00
  📋 Manifest:
    Mem0 Data Backup Manifest
    ========================
    Backup Name: mem0_backup_20241201_143000
    Timestamp: 20241201_143000
    Created: Sun Dec  1 14:30:00 CST 2024
  📦 Contents:
    MANIFEST.txt (2.1KB)
    host_data.tar.gz (156MB)
    volume_qdrant-data.tar.gz (45MB)
    volume_neo4j-data.tar.gz (32MB)
    database_stats.json (0.8KB)
```

### 执行数据恢复

#### 完整恢复
```bash
# 交互式恢复（推荐）
./scripts/restore-data.sh mem0_backup_20241201_143000

# 自动确认恢复
./scripts/restore-data.sh mem0_backup_20241201_143000 -y

# 恢复过程输出
⚠️  This will REPLACE all existing data with backup: mem0_backup_20241201_143000
⚠️  Existing data will be backed up to ./data.backup.TIMESTAMP

Are you sure you want to continue? (y/N): y

[INFO] Starting restore process for: mem0_backup_20241201_143000
[INFO] Stopping services...
[SUCCESS] Services stopped
[WARNING] Cleaning up existing data...
[INFO] Backing up existing data to ./data.backup.1701419400
[SUCCESS] Existing data cleanup completed
[INFO] Restoring host data...
[SUCCESS] Host data restored
[INFO] Restoring Docker volumes...
[INFO] Restoring volume: qdrant-data
[SUCCESS] Volume qdrant-data restored
[INFO] Restoring volume: neo4j-data
[SUCCESS] Volume neo4j-data restored
[INFO] Restoring configuration files...
[SUCCESS] Configuration files restored
[INFO] Starting services...
[INFO] Waiting for services to be ready...
[SUCCESS] Services started and ready
[INFO] Verifying restore results...
[SUCCESS] API health check passed
[SUCCESS] Restore verification completed
[SUCCESS] ✅ Restore completed successfully!

Services Status:
NAME              COMMAND                  SERVICE             STATUS              PORTS
mem0-api          "uvicorn main:app --…"   mem0-api            running             0.0.0.0:8000->8000/tcp
mem0-neo4j        "tini -g -- /startup…"   neo4j               running             0.0.0.0:7474->7474/tcp, 0.0.0.0:7687->7687/tcp
mem0-qdrant       "./entrypoint.sh --w…"   qdrant              running             0.0.0.0:6333->6333/tcp, 0.0.0.0:6334->6334/tcp

API Health: http://localhost:8000/health
```

### 选择性恢复

#### 仅恢复特定组件
```bash
# 手动解压特定组件
cd backups/mem0_backup_20241201_143000

# 恢复SQLite数据库
tar -xzf host_data.tar.gz mem0/history.db
cp mem0/history.db ../../data/mem0/

# 恢复配置文件
cp configs/docker-compose.yaml ../../
cp configs/.env ../../

# 重启服务
docker-compose restart mem0-api
```

### 灾难恢复

#### 完全重建场景
```bash
# 1. 全新环境准备
git clone <repository> /opt/mem0ai
cd /opt/mem0ai/server

# 2. 恢复配置
./scripts/restore-data.sh mem0_backup_20241201_143000 -y

# 3. 验证恢复
./scripts/verify-data.sh

# 4. 启动服务
docker-compose up -d
```

#### 跨服务器迁移
```bash
# 源服务器
./scripts/backup-data.sh -d /tmp
scp -r /tmp/mem0_backup_* user@target-server:/tmp/

# 目标服务器
cd /opt/mem0ai/server
mkdir -p backups
mv /tmp/mem0_backup_* backups/
./scripts/restore-data.sh mem0_backup_20241201_143000 -y
```

## 🔍 数据验证与维护

### 数据完整性验证

#### 完整系统验证
```bash
# 运行完整验证
./scripts/verify-data.sh

# 验证输出示例
Mem0 System Verification
=======================
Timestamp: 2024-12-01 15:30:00
Compose file: docker-compose.yaml

[INFO] Verifying Docker environment...
  Checking Docker daemon... ✓
  Checking Docker Compose... ✓
  Checking Compose file exists... ✓

[INFO] Verifying service status...
  Checking Service running: mem0-api... ✓
  Checking Service running: mem0-qdrant... ✓
  Checking Service running: mem0-neo4j... ✓

[INFO] Verifying data integrity...
  Checking Data directory exists... ✓
  Checking Mem0 data directory... ✓
  SQLite database: ✓ (3 tables, 1247 records)
  API functionality: ✓ (1247 memories)

[INFO] Verifying network connectivity...
  Checking Port 8000 (API)... ✓
  Checking Port 6333 (Qdrant)... ✓
  Checking Port 7474 (Neo4j HTTP)... ✓
  Checking Port 7687 (Neo4j Bolt)... ✓

[INFO] Verification Summary
====================
  Total checks: 15
  Passed: 15
  Failed: 0
  Warnings: 0

  Success rate: 100%
[SUCCESS] ✅ All checks passed! System is healthy.
```

#### 快速健康检查
```bash
# 快速检查（跳过性能测试）
./scripts/verify-data.sh --quick

# 详细诊断
./scripts/verify-data.sh --detailed

# 自动修复权限问题
./scripts/verify-data.sh --fix-permissions
```

### 权限维护

#### 权限问题诊断
```bash
# 检查当前权限
./scripts/fix-permissions.sh --dry-run

# 输出示例
Mem0 Permission Fix
==================
Data directory: ./data
User ID: 1000
Group ID: 1000
Compose file: docker-compose.yaml

Checking current permissions...
  Data directory (./data):
    Owner: 1000:1000
    Permissions: 755
  Database file (./data/mem0/history.db):
    Owner: 1000:1000
    Permissions: 664
  Script file (scripts/backup-data.sh): 755
  Total permission issues found: 0
```

#### 权限自动修复
```bash
# 修复主机权限问题
./scripts/fix-permissions.sh

# 指定特定用户
./scripts/fix-permissions.sh -u 1000 -g 1000

# 修复容器权限
docker-compose exec mem0-api /app/src/scripts/fix-permissions-entrypoint.sh
```

## 📊 监控与告警

### 备份监控

#### 备份状态检查
```bash
# 检查最近的备份
LATEST_BACKUP=$(ls -t backups/ | head -1)
echo "Latest backup: $LATEST_BACKUP"

# 验证备份完整性
./scripts/restore-data.sh "$LATEST_BACKUP" --dry-run
```

#### 自动告警脚本
```bash
#!/bin/bash
# backup-monitor.sh - 备份监控脚本

BACKUP_DIR="./backups"
MAX_AGE_HOURS=24
ALERT_EMAIL="<EMAIL>"

# 检查最新备份时间
LATEST_BACKUP=$(ls -t "$BACKUP_DIR" | head -1)
if [ -z "$LATEST_BACKUP" ]; then
    echo "❌ No backups found!" | mail -s "Mem0 Backup Alert" "$ALERT_EMAIL"
    exit 1
fi

BACKUP_TIME=$(stat -c %Y "$BACKUP_DIR/$LATEST_BACKUP")
CURRENT_TIME=$(date +%s)
AGE_HOURS=$(( (CURRENT_TIME - BACKUP_TIME) / 3600 ))

if [ $AGE_HOURS -gt $MAX_AGE_HOURS ]; then
    echo "⚠️  Latest backup is $AGE_HOURS hours old" | \
         mail -s "Mem0 Backup Alert" "$ALERT_EMAIL"
fi
```

### 存储空间监控

#### 磁盘使用检查
```bash
# 检查数据目录大小
du -sh ./data
# 输出示例: 1.2G    ./data

# 检查可用空间
df -h .
# 输出示例:
# Filesystem      Size  Used Avail Use% Mounted on
# /dev/sda1       50G   15G   33G  32% /

# 存储使用趋势
./scripts/verify-data.sh --detailed | grep -A 5 "storage health"
```

#### 清理脚本
```bash
#!/bin/bash
# cleanup-data.sh - 数据清理脚本

# 清理旧日志
find ./logs -name "*.log" -mtime +7 -delete

# 清理旧备份
find ./backups -name "mem0_backup_*" -mtime +30 -exec rm -rf {} \;

# 清理Docker
docker system prune -f
docker volume prune -f

echo "Cleanup completed"
```

## 🚨 故障恢复场景

### 场景1：数据库损坏

#### 症状
- API返回数据库错误
- SQLite文件无法打开
- 验证脚本报告数据库损坏

#### 恢复步骤
```bash
# 1. 停止服务
docker-compose down

# 2. 备份损坏的数据库
mv ./data/mem0/history.db ./data/mem0/history.db.corrupted

# 3. 从备份恢复数据库
LATEST_BACKUP=$(ls -t backups/ | head -1)
cd backups/$LATEST_BACKUP
tar -xzf host_data.tar.gz mem0/history.db
cp mem0/history.db ../../data/mem0/

# 4. 重启服务
cd ../..
docker-compose up -d

# 5. 验证恢复
./scripts/verify-data.sh
```

### 场景2：容器无法启动

#### 症状
- Docker容器启动失败
- 权限被拒绝错误
- 数据目录无法访问

#### 恢复步骤
```bash
# 1. 检查权限
./scripts/fix-permissions.sh --dry-run

# 2. 修复权限
./scripts/fix-permissions.sh

# 3. 检查数据完整性
./scripts/verify-data.sh

# 4. 重新构建容器
docker-compose down
docker-compose build --no-cache
docker-compose up -d
```

### 场景3：数据丢失

#### 症状
- 数据目录为空
- API返回无数据
- 内存记录丢失

#### 恢复步骤
```bash
# 1. 立即停止服务防止进一步损坏
docker-compose down

# 2. 选择最佳备份
./scripts/restore-data.sh --list

# 3. 执行完整恢复
./scripts/restore-data.sh mem0_backup_20241201_143000 -y

# 4. 验证恢复结果
./scripts/verify-data.sh --detailed
```

## 📚 最佳实践

### 1. 备份策略
- **频率**: 每日自动备份，重要操作前手动备份
- **保留**: 保留最近30天的备份
- **存储**: 本地备份 + 远程存储
- **验证**: 定期验证备份完整性

### 2. 监控告警
- **健康检查**: 每5分钟检查API健康状态
- **存储监控**: 磁盘使用超过80%时告警
- **备份监控**: 超过24小时无备份时告警
- **性能监控**: API响应时间超过2秒时告警

### 3. 维护计划
- **每周**: 运行完整数据验证
- **每月**: 清理旧日志和备份
- **每季度**: 测试灾难恢复流程
- **每年**: 审查和更新备份策略

### 4. 安全考虑
- **加密**: 敏感备份文件加密存储
- **访问控制**: 限制备份文件访问权限
- **传输安全**: 使用安全协议传输备份
- **审计**: 记录所有备份恢复操作

---

## 📞 获取帮助

如遇到问题，请按以下步骤收集信息：

```bash
# 生成诊断报告
./scripts/verify-data.sh --detailed > diagnosis.txt

# 收集服务日志
docker-compose logs > service.log

# 打包支持信息
tar -czf mem0-support-$(date +%Y%m%d).tar.gz \
  diagnosis.txt service.log .env docker-compose.yaml \
  backups/*/MANIFEST.txt
```

联系支持时请提供生成的支持包文件。