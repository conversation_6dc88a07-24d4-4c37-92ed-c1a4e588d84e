#!/usr/bin/env python3
"""
指令管理数据验证器测试脚本
用于测试验证器的各种功能
"""

import json
import sys
import os
from pathlib import Path

# 添加server目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__)))

from instruction_data_validator import InstructionDataValidator, InstructionTemplate, InstructionCategory

def test_valid_data():
    """测试有效数据"""
    print("🧪 测试有效数据...")
    
    templates = [
        {
            'id': 'template_1',
            'name': '测试模板',
            'description': '这是一个测试模板',
            'content': '请根据 {{input}} 生成相应的回答',
            'category': 'general',
            'tags': ['测试', '示例'],
            'isActive': True,
            'createdAt': '2024-01-01T00:00:00.000Z',
            'updatedAt': '2024-01-01T00:00:00.000Z'
        }
    ]
    
    categories = [
        {
            'id': 'general',
            'name': '通用指令',
            'description': '适用于各种场景的通用指令模板',
            'color': '#00d4aa'
        }
    ]
    
    validator = InstructionDataValidator(templates, categories)
    result = validator.validate_all()
    
    assert result['is_valid'] == True, "有效数据应该通过验证"
    assert result['summary']['error_count'] == 0, "有效数据不应该有错误"
    print("✅ 有效数据测试通过")

def test_invalid_template_name():
    """测试无效的模板名称"""
    print("🧪 测试无效的模板名称...")
    
    templates = [
        {
            'id': 'template_1',
            'name': '',  # 空名称
            'description': '这是一个测试模板',
            'content': '请根据 {{input}} 生成相应的回答',
            'category': 'general',
            'tags': ['测试'],
            'isActive': True,
            'createdAt': '2024-01-01T00:00:00.000Z',
            'updatedAt': '2024-01-01T00:00:00.000Z'
        }
    ]
    
    categories = [
        {
            'id': 'general',
            'name': '通用指令',
            'description': '适用于各种场景的通用指令模板',
            'color': '#00d4aa'
        }
    ]
    
    validator = InstructionDataValidator(templates, categories)
    result = validator.validate_all()
    
    assert result['is_valid'] == False, "无效数据应该验证失败"
    assert result['summary']['error_count'] > 0, "应该有错误"
    assert any('名称不能为空' in error for error in result['errors']), "应该检测到名称为空的错误"
    print("✅ 无效模板名称测试通过")

def test_invalid_variable():
    """测试无效的变量占位符"""
    print("🧪 测试无效的变量占位符...")
    
    templates = [
        {
            'id': 'template_1',
            'name': '测试模板',
            'description': '这是一个测试模板',
            'content': '请根据 {{123invalid}} 生成回答',  # 无效变量名
            'category': 'general',
            'tags': ['测试'],
            'isActive': True,
            'createdAt': '2024-01-01T00:00:00.000Z',
            'updatedAt': '2024-01-01T00:00:00.000Z'
        }
    ]
    
    categories = [
        {
            'id': 'general',
            'name': '通用指令',
            'description': '适用于各种场景的通用指令模板',
            'color': '#00d4aa'
        }
    ]
    
    validator = InstructionDataValidator(templates, categories)
    result = validator.validate_all()
    
    assert result['is_valid'] == False, "无效变量应该验证失败"
    assert any('变量名' in error and '123invalid' in error for error in result['errors']), "应该检测到无效变量名"
    print("✅ 无效变量占位符测试通过")

def test_missing_category():
    """测试引用不存在的分类"""
    print("🧪 测试引用不存在的分类...")
    
    templates = [
        {
            'id': 'template_1',
            'name': '测试模板',
            'description': '这是一个测试模板',
            'content': '请根据 {{input}} 生成相应的回答',
            'category': 'nonexistent',  # 不存在的分类
            'tags': ['测试'],
            'isActive': True,
            'createdAt': '2024-01-01T00:00:00.000Z',
            'updatedAt': '2024-01-01T00:00:00.000Z'
        }
    ]
    
    categories = [
        {
            'id': 'general',
            'name': '通用指令',
            'description': '适用于各种场景的通用指令模板',
            'color': '#00d4aa'
        }
    ]
    
    validator = InstructionDataValidator(templates, categories)
    result = validator.validate_all()
    
    assert result['is_valid'] == False, "引用不存在的分类应该验证失败"
    assert any('引用了不存在的分类' in error for error in result['errors']), "应该检测到分类引用错误"
    print("✅ 缺失分类引用测试通过")

def test_invalid_category_color():
    """测试无效的分类颜色"""
    print("🧪 测试无效的分类颜色...")
    
    templates = [
        {
            'id': 'template_1',
            'name': '测试模板',
            'description': '这是一个测试模板',
            'content': '请根据 {{input}} 生成相应的回答',
            'category': 'general',
            'tags': ['测试'],
            'isActive': True,
            'createdAt': '2024-01-01T00:00:00.000Z',
            'updatedAt': '2024-01-01T00:00:00.000Z'
        }
    ]
    
    categories = [
        {
            'id': 'general',
            'name': '通用指令',
            'description': '适用于各种场景的通用指令模板',
            'color': 'invalid-color'  # 无效颜色格式
        }
    ]
    
    validator = InstructionDataValidator(templates, categories)
    result = validator.validate_all()
    
    assert result['is_valid'] == False, "无效颜色应该验证失败"
    assert any('颜色格式无效' in error for error in result['errors']), "应该检测到颜色格式错误"
    print("✅ 无效分类颜色测试通过")

def test_duplicate_names():
    """测试重复名称检测"""
    print("🧪 测试重复名称检测...")
    
    templates = [
        {
            'id': 'template_1',
            'name': '测试模板',
            'description': '这是第一个测试模板',
            'content': '请根据 {{input}} 生成相应的回答',
            'category': 'general',
            'tags': ['测试'],
            'isActive': True,
            'createdAt': '2024-01-01T00:00:00.000Z',
            'updatedAt': '2024-01-01T00:00:00.000Z'
        },
        {
            'id': 'template_2',
            'name': '测试模板',  # 重复名称
            'description': '这是第二个测试模板',
            'content': '请根据 {{input}} 生成另一个回答',
            'category': 'general',
            'tags': ['测试'],
            'isActive': True,
            'createdAt': '2024-01-01T00:00:00.000Z',
            'updatedAt': '2024-01-01T00:00:00.000Z'
        }
    ]
    
    categories = [
        {
            'id': 'general',
            'name': '通用指令',
            'description': '适用于各种场景的通用指令模板',
            'color': '#00d4aa'
        }
    ]
    
    validator = InstructionDataValidator(templates, categories)
    result = validator.validate_all()
    
    assert result['is_valid'] == True, "重复名称应该只产生警告，不影响验证通过"
    assert result['summary']['warning_count'] > 0, "应该有警告"
    assert any('模板名称重复' in warning for warning in result['warnings']), "应该检测到重复名称"
    print("✅ 重复名称检测测试通过")

def test_data_fix():
    """测试数据修复功能"""
    print("🧪 测试数据修复功能...")
    
    templates = [
        {
            'id': 'template_1',
            'name': '测试模板',
            'description': '这是一个测试模板',
            'content': '请根据 {{input}} 生成相应的回答',
            'category': 'general',
            'tags': ['测试', '示例'],
            'isActive': True,
            'createdAt': '',  # 缺失时间戳
            'updatedAt': ''   # 缺失时间戳
        }
    ]
    
    categories = [
        {
            'id': 'general',
            'name': '通用指令',
            'description': '适用于各种场景的通用指令模板',
            'color': '#00d4aa'
        }
    ]
    
    validator = InstructionDataValidator(templates, categories)
    fix_result = validator.fix_common_issues()
    
    assert len(fix_result['fixes']) > 0, "应该有修复操作"
    assert any('添加了创建时间' in fix for fix in fix_result['fixes']), "应该修复创建时间"
    assert any('添加了更新时间' in fix for fix in fix_result['fixes']), "应该修复更新时间"
    
    # 验证修复后的数据
    fixed_template = fix_result['fixed_templates'][0]
    assert fixed_template['createdAt'] != '', "创建时间应该被修复"
    assert fixed_template['updatedAt'] != '', "更新时间应该被修复"
    
    print("✅ 数据修复功能测试通过")

def run_all_tests():
    """运行所有测试"""
    print("🚀 开始运行指令管理数据验证器测试")
    print("=" * 50)
    
    tests = [
        test_valid_data,
        test_invalid_template_name,
        test_invalid_variable,
        test_missing_category,
        test_invalid_category_color,
        test_duplicate_names,
        test_data_fix
    ]
    
    passed = 0
    failed = 0
    
    for test in tests:
        try:
            test()
            passed += 1
        except Exception as e:
            print(f"❌ {test.__name__} 失败: {e}")
            failed += 1
    
    print("=" * 50)
    print(f"📊 测试结果: {passed} 通过, {failed} 失败")
    
    if failed == 0:
        print("🎉 所有测试通过！")
        return True
    else:
        print("💥 有测试失败，请检查代码")
        return False

if __name__ == "__main__":
    success = run_all_tests()
    sys.exit(0 if success else 1)
