# Mem0 故障排查指南

## 🎯 概述

本指南提供了 Mem0 系统常见问题的诊断和解决方案，帮助您快速定位和修复问题。

## 🔧 快速诊断工具

### 一键诊断命令
```bash
# 运行完整系统诊断
./scripts/verify-data.sh --detailed

# 快速健康检查
curl -s http://localhost:8000/health | jq

# 检查服务状态
docker-compose ps

# 查看近期日志
docker-compose logs --tail=100 mem0-api
```

### 生成诊断报告
```bash
#!/bin/bash
# 自动生成完整诊断报告
echo "=== Mem0 Diagnostic Report ===" > diagnosis.txt
echo "Generated: $(date)" >> diagnosis.txt
echo "" >> diagnosis.txt

echo "=== System Information ===" >> diagnosis.txt
uname -a >> diagnosis.txt
df -h >> diagnosis.txt
free -h >> diagnosis.txt
echo "" >> diagnosis.txt

echo "=== Docker Status ===" >> diagnosis.txt
docker version >> diagnosis.txt
docker-compose ps >> diagnosis.txt
echo "" >> diagnosis.txt

echo "=== Service Health ===" >> diagnosis.txt
curl -s http://localhost:8000/health >> diagnosis.txt
echo "" >> diagnosis.txt

echo "=== Data Validation ===" >> diagnosis.txt
./scripts/verify-data.sh >> diagnosis.txt

echo "Diagnostic report saved to: diagnosis.txt"
```

## 🚨 常见问题分类

### 1. 启动问题

#### 问题：容器无法启动
**症状**
```
ERROR: for mem0-api  Cannot start service mem0-api: OCI runtime create failed
```

**诊断步骤**
```bash
# 检查Docker状态
docker info

# 检查端口占用
netstat -tlnp | grep :8000

# 检查资源使用
docker stats

# 查看详细错误
docker-compose up --no-deps mem0-api
```

**解决方案**
```bash
# 方案1: 释放端口
sudo lsof -ti:8000 | xargs kill -9

# 方案2: 清理Docker资源
docker system prune -f
docker volume prune -f

# 方案3: 重建容器
docker-compose down
docker-compose build --no-cache
docker-compose up -d
```

#### 问题：权限被拒绝
**症状**
```
PermissionError: [Errno 13] Permission denied: '/app/data'
```

**诊断步骤**
```bash
# 检查文件权限
ls -la ./data
ls -la ./data/mem0/

# 检查用户ID映射
id
docker-compose exec mem0-api id

# 检查SELinux状态（如果适用）
getenforce
```

**解决方案**
```bash
# 方案1: 自动修复权限
./scripts/fix-permissions.sh

# 方案2: 手动修复权限
sudo chown -R $USER:$USER ./data ./logs ./config
chmod -R 755 ./data
chmod 664 ./data/mem0/history.db

# 方案3: 设置正确的用户ID
export PUID=$(id -u)
export PGID=$(id -g)
docker-compose down && docker-compose up -d
```

### 2. 数据库连接问题

#### 问题：Qdrant连接失败
**症状**
```
ConnectionError: Cannot connect to Qdrant at qdrant:6333
```

**诊断步骤**
```bash
# 检查Qdrant服务状态
docker-compose ps qdrant

# 检查网络连通性
docker-compose exec mem0-api nc -z qdrant 6333

# 检查Qdrant健康状态
curl http://localhost:6333/health

# 查看Qdrant日志
docker-compose logs qdrant
```

**解决方案**
```bash
# 方案1: 重启Qdrant服务
docker-compose restart qdrant

# 方案2: 检查网络配置
docker network ls
docker network inspect mem0-network

# 方案3: 重建网络
docker-compose down
docker network prune -f
docker-compose up -d

# 方案4: 检查防火墙
sudo ufw status
sudo iptables -L
```

#### 问题：Neo4j连接失败
**症状**
```
ServiceUnavailable: Failed to establish connection to Neo4j
```

**诊断步骤**
```bash
# 检查Neo4j服务状态
docker-compose ps neo4j

# 检查Neo4j日志
docker-compose logs neo4j

# 测试连接
docker-compose exec mem0-api nc -z neo4j 7687

# 检查认证配置
curl -u neo4j:mem0graph http://localhost:7474/
```

**解决方案**
```bash
# 方案1: 等待Neo4j完全启动
docker-compose logs -f neo4j | grep "Started"

# 方案2: 重置Neo4j数据
docker-compose down
docker volume rm mem0-stack_neo4j-data
docker-compose up -d

# 方案3: 检查认证配置
echo "NEO4J_AUTH=neo4j/mem0graph" >> .env
docker-compose restart neo4j

# 方案4: 手动初始化
docker-compose exec neo4j cypher-shell -u neo4j -p mem0graph "SHOW DATABASES"
```

### 3. API响应问题

#### 问题：API响应缓慢
**症状**
- 请求超时
- 响应时间 > 30秒
- 内存使用过高

**诊断步骤**
```bash
# 检查API响应时间
time curl http://localhost:8000/health

# 检查资源使用
docker stats mem0-api

# 检查进程状态
docker-compose exec mem0-api ps aux

# 分析慢查询
docker-compose logs mem0-api | grep -i "slow\|timeout"
```

**解决方案**
```bash
# 方案1: 增加资源限制
# 编辑 docker-compose.yaml
services:
  mem0-api:
    deploy:
      resources:
        limits:
          memory: 4G
          cpus: '2.0'

# 方案2: 优化配置
export API_WORKERS=4
export MAX_REQUESTS=1000
docker-compose restart mem0-api

# 方案3: 清理缓存
docker-compose exec mem0-api python -c "
import sqlite3
conn = sqlite3.connect('/app/data/history.db')
conn.execute('VACUUM')
conn.close()
"

# 方案4: 重启服务
docker-compose restart mem0-api
```

#### 问题：API返回500错误
**症状**
```json
{
  "detail": "Internal Server Error"
}
```

**诊断步骤**
```bash
# 查看详细错误日志
docker-compose logs mem0-api | tail -50

# 检查API健康状态
curl -v http://localhost:8000/health

# 测试数据库连接
docker-compose exec mem0-api python -c "
import sqlite3
try:
    conn = sqlite3.connect('/app/data/history.db')
    print('Database connection: OK')
    conn.close()
except Exception as e:
    print(f'Database error: {e}')
"

# 检查内存实例
docker-compose exec mem0-api python -c "
try:
    from mem0 import Memory
    memory = Memory()
    print('Memory instance: OK')
except Exception as e:
    print(f'Memory error: {e}')
"
```

**解决方案**
```bash
# 方案1: 检查配置文件
./scripts/verify-data.sh

# 方案2: 重新初始化数据
./scripts/restore-data.sh [latest_backup] -y

# 方案3: 重新构建服务
docker-compose down
docker-compose build --no-cache mem0-api
docker-compose up -d

# 方案4: 检查环境变量
docker-compose exec mem0-api env | grep -E "(OPENAI|MEM0|NEO4J)"
```

### 4. 内存和存储问题

#### 问题：内存不足
**症状**
```
MemoryError: Unable to allocate memory
OOMKilled
```

**诊断步骤**
```bash
# 检查系统内存
free -h

# 检查容器内存使用
docker stats

# 检查内存限制
docker-compose config | grep -A 5 -B 5 memory

# 查看OOM日志
dmesg | grep -i "killed process"
journalctl -u docker | grep -i oom
```

**解决方案**
```bash
# 方案1: 增加内存限制
# 编辑 docker-compose.yaml
deploy:
  resources:
    limits:
      memory: 8G
    reservations:
      memory: 4G

# 方案2: 优化内存使用
export PYTHONHASHSEED=0
export MALLOC_TRIM_THRESHOLD_=100000
docker-compose restart mem0-api

# 方案3: 启用交换分区
sudo swapon --show
sudo fallocate -l 4G /swapfile
sudo chmod 600 /swapfile
sudo mkswap /swapfile
sudo swapon /swapfile

# 方案4: 清理内存
docker system prune -f
echo 3 | sudo tee /proc/sys/vm/drop_caches
```

#### 问题：磁盘空间不足
**症状**
```
OSError: [Errno 28] No space left on device
```

**诊断步骤**
```bash
# 检查磁盘使用
df -h
du -sh ./data ./logs

# 检查Docker空间使用
docker system df

# 查找大文件
find ./data -type f -size +100M -exec ls -lh {} \;

# 检查inode使用
df -i
```

**解决方案**
```bash
# 方案1: 清理Docker
docker system prune -a -f
docker volume prune -f
docker image prune -a -f

# 方案2: 清理应用数据
# 清理旧日志
find ./logs -name "*.log" -mtime +7 -delete

# 清理旧备份
find ./backups -name "mem0_backup_*" -mtime +30 -exec rm -rf {} \;

# 方案3: 压缩数据库
docker-compose exec mem0-api python -c "
import sqlite3
conn = sqlite3.connect('/app/data/history.db')
conn.execute('VACUUM')
conn.close()
print('Database vacuumed')
"

# 方案4: 移动数据到更大分区
sudo mv ./data /mnt/large_partition/mem0_data
ln -s /mnt/large_partition/mem0_data ./data
```

### 5. 网络问题

#### 问题：端口被占用
**症状**
```
ERROR: for mem0-api  Cannot start service: Bind for 0.0.0.0:8000 failed: port is already allocated
```

**诊断步骤**
```bash
# 检查端口占用
netstat -tlnp | grep :8000
ss -tlnp | grep :8000
lsof -i :8000

# 检查Docker端口映射
docker ps --format "table {{.Names}}\t{{.Ports}}"
```

**解决方案**
```bash
# 方案1: 终止占用进程
sudo lsof -ti:8000 | xargs kill -9

# 方案2: 更改端口
echo "API_PORT=8001" >> .env
docker-compose down && docker-compose up -d

# 方案3: 清理僵尸容器
docker container prune -f
```

#### 问题：容器间网络不通
**症状**
```
Name or service not known: qdrant
Connection refused
```

**诊断步骤**
```bash
# 检查网络配置
docker network ls
docker network inspect mem0-network

# 测试容器间连通性
docker-compose exec mem0-api ping qdrant
docker-compose exec mem0-api nslookup qdrant

# 检查DNS解析
docker-compose exec mem0-api cat /etc/resolv.conf
```

**解决方案**
```bash
# 方案1: 重建网络
docker-compose down
docker network prune -f
docker-compose up -d

# 方案2: 使用IP地址连接
QDRANT_IP=$(docker inspect -f '{{range .NetworkSettings.Networks}}{{.IPAddress}}{{end}}' mem0-qdrant)
echo "QDRANT_HOST=$QDRANT_IP" >> .env

# 方案3: 检查防火墙规则
sudo ufw status
sudo iptables -L DOCKER-USER
```

## 🔍 高级诊断技巧

### 1. 实时监控
```bash
# 实时查看日志
docker-compose logs -f

# 监控资源使用
watch -n 2 'docker stats --no-stream'

# 监控API健康
watch -n 5 'curl -s http://localhost:8000/health | jq .status'

# 监控磁盘使用
watch -n 10 'df -h | grep -E "/$|/opt"'
```

### 2. 性能分析
```bash
# API性能测试
curl -o /dev/null -s -w "%{time_total}\n" http://localhost:8000/health

# 并发测试
for i in {1..10}; do
  curl -s http://localhost:8000/health > /dev/null &
done
wait

# 内存分析
docker-compose exec mem0-api python -c "
import psutil
import os
process = psutil.Process(os.getpid())
print(f'Memory: {process.memory_info().rss / 1024 / 1024:.2f}MB')
print(f'CPU: {process.cpu_percent()}%')
"
```

### 3. 数据库分析
```bash
# SQLite数据库分析
docker-compose exec mem0-api python -c "
import sqlite3
conn = sqlite3.connect('/app/data/history.db')
cursor = conn.cursor()

# 检查表结构
cursor.execute('SELECT sql FROM sqlite_master WHERE type=\"table\"')
for table in cursor.fetchall():
    print(table[0])

# 检查数据统计
cursor.execute('SELECT name FROM sqlite_master WHERE type=\"table\"')
tables = cursor.fetchall()
for table in tables:
    cursor.execute(f'SELECT COUNT(*) FROM {table[0]}')
    count = cursor.fetchone()[0]
    print(f'{table[0]}: {count} records')

conn.close()
"

# Qdrant集合信息
curl -s http://localhost:6333/collections | jq

# Neo4j节点统计
docker-compose exec neo4j cypher-shell -u neo4j -p mem0graph \
  "MATCH (n) RETURN labels(n) as label, count(n) as count"
```

## 📋 问题报告模板

当需要报告问题时，请提供以下信息：

```markdown
### 环境信息
- OS: [Linux/macOS/Windows]
- Docker版本: [docker --version]
- Docker Compose版本: [docker-compose --version]
- Mem0版本: [从docker-compose.yaml中获取]

### 问题描述
[详细描述遇到的问题]

### 复现步骤
1. [步骤1]
2. [步骤2]
3. [步骤3]

### 错误信息
```
[粘贴完整的错误日志]
```

### 系统状态
```bash
# 运行以下命令并粘贴输出
./scripts/verify-data.sh
docker-compose ps
docker-compose logs --tail=50
```

### 已尝试的解决方案
[列出已经尝试的解决方案]
```

## 🆘 紧急恢复程序

### 完全系统崩溃恢复
```bash
#!/bin/bash
# emergency-recovery.sh - 紧急恢复脚本

echo "🚨 Starting emergency recovery..."

# 1. 停止所有服务
docker-compose down --remove-orphans

# 2. 备份当前状态
mv ./data ./data.crashed.$(date +%s) 2>/dev/null || true

# 3. 寻找最新备份
LATEST_BACKUP=$(ls -t backups/ | head -1)
if [ -z "$LATEST_BACKUP" ]; then
    echo "❌ No backups found! Manual intervention required."
    exit 1
fi

echo "📦 Using backup: $LATEST_BACKUP"

# 4. 恢复数据
./scripts/restore-data.sh "$LATEST_BACKUP" -y

# 5. 验证恢复
if ./scripts/verify-data.sh --quick; then
    echo "✅ Emergency recovery completed successfully"
else
    echo "⚠️ Recovery completed with warnings"
fi

echo "🔍 Check system status:"
echo "  curl http://localhost:8000/health"
echo "  docker-compose ps"
```

### 数据损坏快速修复
```bash
#!/bin/bash
# quick-fix.sh - 快速修复脚本

echo "🔧 Starting quick fix..."

# 修复权限
./scripts/fix-permissions.sh

# 重建容器
docker-compose down
docker-compose build --no-cache
docker-compose up -d

# 等待服务启动
sleep 30

# 验证修复
if curl -sf http://localhost:8000/health > /dev/null; then
    echo "✅ Quick fix successful"
else
    echo "❌ Quick fix failed, running emergency recovery"
    ./emergency-recovery.sh
fi
```

## 📞 获取帮助

### 收集诊断信息
```bash
# 生成完整支持包
./scripts/verify-data.sh --detailed > diagnosis.txt
docker-compose logs > service.log
docker-compose config > config.yaml

tar -czf mem0-support-$(date +%Y%m%d_%H%M%S).tar.gz \
    diagnosis.txt service.log config.yaml \
    .env docker-compose.yaml \
    backups/*/MANIFEST.txt 2>/dev/null || true
```

### 联系支持前检查清单
- [ ] 已运行完整诊断 `./scripts/verify-data.sh --detailed`
- [ ] 已收集服务日志 `docker-compose logs`
- [ ] 已检查系统资源 `df -h && free -h`
- [ ] 已尝试基本故障排除步骤
- [ ] 已备份当前数据状态

---

💡 **提示**: 大多数问题都可以通过运行 `./scripts/verify-data.sh --fix-permissions` 解决。如果问题持续存在，请生成诊断报告并联系支持团队。