# =============================================================================
# Mem0 环境变量配置示例
# 复制为 .env 文件并修改相应的值
# =============================================================================

# =============================
# 基础配置
# =============================
# 环境类型 (development/production)
ENVIRONMENT=production

# 时区设置
TZ=Asia/Shanghai

# =============================
# 服务端口配置
# =============================
# API服务端口
API_PORT=8000

# Qdrant数据库端口
QDRANT_PORT=6333
QDRANT_GRPC_PORT=6334

# Neo4j数据库端口
NEO4J_HTTP_PORT=7474
NEO4J_BOLT_PORT=7687

# =============================
# 数据库配置
# =============================
# Neo4j认证
NEO4J_USERNAME=neo4j
NEO4J_PASSWORD=mem0graph

# 是否启用图存储
ENABLE_GRAPH_STORE=true

# =============================
# AI服务配置
# =============================
# OpenAI配置
OPENAI_API_KEY=your_openai_api_key_here
OPENAI_BASE_URL=https://api.openai.com/v1
OPENAI_MODEL=gpt-4o-mini
OPENAI_EMBEDDING_MODEL=text-embedding-3-small
OPENAI_TEMPERATURE=0.1
OPENAI_MAX_TOKENS=2000

# 多模态功能配置
OPENAI_ENABLE_VISION=true
OPENAI_VISION_DETAILS=auto

# =============================
# 数据存储配置
# =============================
# 数据目录路径
MEM0_DATA_PATH=/app/data
MEM0_DIR=/app/data/mem0
HISTORY_DB_PATH=/app/data/mem0/history.db

# =============================
# 用户权限配置
# =============================
# 主机用户ID和组ID（避免权限问题）
PUID=1000
PGID=1000

# =============================
# 性能和调试配置
# =============================
# 调试模式
DEBUG=true
LOG_LEVEL=INFO

# 数据完整性验证（生产环境建议启用，开发环境可禁用以加快启动）
ENABLE_DATA_VALIDATION=false

# API工作进程数（生产环境）
API_WORKERS=4
MAX_REQUESTS=1000
MAX_REQUESTS_JITTER=100

# =============================
# 备份配置
# =============================
# 备份保留天数
BACKUP_RETENTION_DAYS=7

# =============================
# 可选服务配置
# =============================
# 数据库管理工具端口
ADMINER_PORT=8080

# HTTP/HTTPS端口（如果使用Nginx）
HTTP_PORT=80
HTTPS_PORT=443

# =============================
# 开发环境特定配置
# =============================
# 是否启用代码热重载
RELOAD_ON_CHANGE=true

# 是否强制多模态配置
FORCE_MULTIMODAL_CONFIG=false