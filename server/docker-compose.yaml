# =============================================================================
# Mem0 Docker 配置 - 主配置文件（推荐配置）
# 使用混合但清晰可控的Docker卷方案，解决路径冲突问题
# =============================================================================

name: mem0-stack

services:
  # Mem0 API 服务
  mem0-api:
    build:
      context: ..  # 设置上下文为父目录
      dockerfile: server/Dockerfile
    container_name: mem0-api
    ports:
      - "${API_PORT:-8000}:8000"
    env_file:
      - .env
    networks:
      - mem0-network
    volumes:
      # 职责分离：代码与数据完全分离
      # 代码挂载 - 仅用于开发环境
      - ./:/app/src:ro                     # 服务器源码（只读）
      - ../mem0:/app/packages/mem0:ro      # Mem0库源码（只读）
      
      # 数据持久化 - 清晰的路径规划
      - ./data:/app/data:rw                # 应用数据目录
      - ./logs:/app/logs:rw                # 应用日志目录
      - ./config:/app/config:rw            # 配置文件目录
    depends_on:
      qdrant:
        condition: service_healthy
      neo4j:
        condition: service_healthy
    healthcheck:
      test: ["CMD", "python", "-c", "import requests; requests.get('http://localhost:8000/health', timeout=5)"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s
    restart: unless-stopped
    environment:
      - PYTHONDONTWRITEBYTECODE=1
      - PYTHONUNBUFFERED=1
      - PYTHONWARNINGS=ignore
      - PYTHONPATH=/app/src:/app/packages  # 清晰的Python路径
      - MEM0_DATA_PATH=/app/data
      - MEM0_DIR=/app/data/mem0
      - HISTORY_DB_PATH=/app/data/mem0/history.db
      - NEO4J_URI=bolt://neo4j:7687
      - NEO4J_USERNAME=neo4j
      - NEO4J_PASSWORD=mem0graph
      - ENABLE_GRAPH_STORE=${ENABLE_GRAPH_STORE:-true}
      - ENABLE_DATA_VALIDATION=${ENABLE_DATA_VALIDATION:-false}  # 控制数据验证启用/禁用，默认禁用以加快启动
      - TZ=Asia/Shanghai
      # 权限和安全配置
      - PUID=${PUID:-1000}
      - PGID=${PGID:-1000}
    entrypoint: ["/app/entrypoint.sh"]
    command: ["uvicorn", "main:app", "--host", "0.0.0.0", "--port", "8000"]
    working_dir: /app/src
    deploy:
      resources:
        limits:
          memory: 4G
          cpus: '2.0'
        reservations:
          memory: 1G
          cpus: '0.5'

  # Qdrant 向量数据库
  qdrant:
    image: qdrant/qdrant:v1.11.3
    container_name: mem0-qdrant
    networks:
      - mem0-network
    ports:
      - "${QDRANT_PORT:-6333}:6333"
      - "${QDRANT_GRPC_PORT:-6334}:6334"
    volumes:
      # 使用主机目录便于数据管理和备份
      - ./data/qdrant:/qdrant/storage:rw
    restart: unless-stopped
    healthcheck:
      test: ["CMD-SHELL", "timeout 5 bash -c 'echo > /dev/tcp/localhost/6333' || exit 1"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 30s
    environment:
      - QDRANT__SERVICE__HTTP_PORT=6333
      - QDRANT__SERVICE__GRPC_PORT=6334
      - QDRANT__LOG_LEVEL=INFO
      - TZ=Asia/Shanghai
    deploy:
      resources:
        limits:
          memory: 4G
          cpus: '2.0'
        reservations:
          memory: 1G
          cpus: '0.5'

  # Neo4j 图数据库
  neo4j:
    image: neo4j:5.26.0
    container_name: mem0-neo4j
    networks:
      - mem0-network
    healthcheck:
      test: ["CMD", "cypher-shell", "-u", "neo4j", "-p", "mem0graph", "RETURN 1"]
      interval: 30s
      timeout: 10s
      retries: 10
      start_period: 60s
    ports:
      - "${NEO4J_HTTP_PORT:-7474}:7474"  # HTTP 端口
      - "${NEO4J_BOLT_PORT:-7687}:7687"  # Bolt 端口
    volumes:
      # 使用主机目录，确保数据持久化和备份方便
      - ./data/neo4j/data:/data:rw
      - ./data/neo4j/logs:/logs:rw
      - ./data/neo4j/import:/var/lib/neo4j/import:rw
      - ./data/neo4j/plugins:/plugins:rw
    restart: unless-stopped
    environment:
      - NEO4J_AUTH=${NEO4J_USERNAME:-neo4j}/${NEO4J_PASSWORD:-mem0graph}
      - NEO4J_PLUGINS=["apoc"]
      - NEO4J_apoc_export_file_enabled=true
      - NEO4J_apoc_import_file_enabled=true
      - NEO4J_apoc_import_file_use__neo4j__config=true
      - NEO4J_dbms_security_procedures_unrestricted=apoc.*
      - NEO4J_dbms_security_procedures_allowlist=apoc.*
      - NEO4J_dbms_memory_heap_initial__size=1G
      - NEO4J_dbms_memory_heap_max__size=4G
      - NEO4J_dbms_memory_pagecache_size=2G
      - NEO4J_dbms_logs_debug_level=INFO
      - TZ=Asia/Shanghai
    deploy:
      resources:
        limits:
          memory: 6G
          cpus: '2.0'
        reservations:
          memory: 2G
          cpus: '1.0'

# 网络配置
networks:
  mem0-network:
    driver: bridge
    name: mem0-network
    driver_opts:
      com.docker.network.bridge.name: mem0-br