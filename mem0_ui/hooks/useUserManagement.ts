import { useState, useCallback, useEffect } from 'react';

import { realMem0Client } from '@/lib/mem0-client';

export interface UserInfo {
  id: string;
  name?: string;
  memory_count: number;
  last_active?: string;
  created_at?: string;
}

export interface UserAnalytics {
  total_memories: number;
  memories_by_category: Record<string, number>;
  activity_timeline: Array<{
    date: string;
    memory_count: number;
  }>;
  most_active_days: string[];
  memory_growth_trend: 'increasing' | 'decreasing' | 'stable';
}

export interface BatchUserOperation {
  type: 'delete' | 'export' | 'archive';
  userIds: string[];
  options?: Record<string, any>;
}

interface UseUserManagementReturn {
  users: UserInfo[];
  currentUserId: string | null;
  isLoading: boolean;
  error: string | null;
  setCurrentUserId: (userId: string) => void;
  fetchUsers: () => Promise<void>;
  getUserStats: (userId: string) => Promise<any>;
  createUser: (userId: string, name?: string) => Promise<{
    user_id: string;
    name?: string;
    metadata?: Record<string, any>;
    initialization_memory_id?: string;
    created_at: string;
    message: string;
    existing?: boolean;
    memory_count?: number;
  }>;
  deleteUser: (userId: string) => Promise<void>;
  // 新增的增强功能
  getUserAnalytics: (userId: string) => Promise<UserAnalytics>;
  exportUserData: (userId: string) => Promise<Blob>;
  batchUserOperations: (operations: BatchUserOperation[]) => Promise<void>;
  getUserMemoryTimeline: (userId: string, days?: number) => Promise<Array<{date: string; count: number}>>;
}

export const useUserManagement = (): UseUserManagementReturn => {
  const [users, setUsers] = useState<UserInfo[]>([]);
  const [currentUserId, setCurrentUserIdState] = useState<string | null>(
    typeof window !== 'undefined' ? localStorage.getItem('mem0_current_user_id') : null
  );
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const setCurrentUserId = useCallback((userId: string) => {
    setCurrentUserIdState(userId);
    if (typeof window !== 'undefined') {
      localStorage.setItem('mem0_current_user_id', userId);
    }
  }, []);

  const fetchUsers = useCallback(async () => {
    setIsLoading(true);
    setError(null);
    try {
      console.log('[fetchUsers] 开始获取用户列表...');

      // 使用真实的用户管理API获取用户列表
      const usersResponse = await realMem0Client.getUsers({
        limit: 100,
        include_stats: true,
        time_range: '24h'
      });

      console.log('[fetchUsers] API响应:', usersResponse);

      // 转换API响应为UserInfo格式
      const usersList: UserInfo[] = usersResponse.users.map(user => ({
        id: user.user_id,
        name: `用户 ${user.user_id}`, // API中没有name字段，使用user_id生成
        memory_count: user.total_memories || 0,
        last_active: user.last_activity || new Date().toISOString(),
        created_at: user.created_at || new Date().toISOString()
      }));

      console.log('[fetchUsers] 转换后的用户列表:', usersList);
      setUsers(usersList);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to fetch users';
      setError(errorMessage);
      console.error('Failed to fetch users:', err);

      // 如果API调用失败，提供一个空的用户列表而不是模拟数据
      setUsers([]);
    } finally {
      setIsLoading(false);
    }
  }, []);

  const getUserStats = useCallback(async (userId: string) => {
    try {
      console.log(`[getUserStats] 获取用户 ${userId} 的统计信息...`);
      return await realMem0Client.getUserStats(userId, '24h');
    } catch (err) {
      console.error(`Failed to get stats for user ${userId}:`, err);
      return { total_memories: 0 };
    }
  }, []);

  const createUser = useCallback(async (userId: string, name?: string) => {
    setIsLoading(true);
    setError(null);
    try {
      // 使用专门的用户创建API端点
      const response = await realMem0Client.createUser({
        user_id: userId,
        name: name || '',
        metadata: { created_via: 'ui', created_at: new Date().toISOString() }
      });

      // 检查是否是已存在的用户
      if (response.existing) {
        console.log(`User ${userId} already exists with ${response.memory_count} memories`);
      } else {
        console.log(`User ${userId} created successfully with memory ID: ${response.initialization_memory_id}`);
      }

      // 更新用户列表
      await fetchUsers();

      return response;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to create user';
      setError(errorMessage);
      throw new Error(errorMessage);
    } finally {
      setIsLoading(false);
    }
  }, [fetchUsers]);

  const deleteUser = useCallback(async (userId: string) => {
    setIsLoading(true);
    setError(null);
    try {
      console.log(`[deleteUser] 删除用户 ${userId}...`);

      // 使用真实的用户删除API
      await realMem0Client.deleteUser(userId);

      // 如果删除的是当前用户，清除当前用户ID
      if (currentUserId === userId) {
        setCurrentUserId('');
      }

      // 更新用户列表
      await fetchUsers();
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to delete user';
      setError(errorMessage);
      throw new Error(errorMessage);
    } finally {
      setIsLoading(false);
    }
  }, [currentUserId, setCurrentUserId, fetchUsers]);

  // 获取用户分析数据
  const getUserAnalytics = useCallback(async (userId: string): Promise<UserAnalytics> => {
    try {
      const memories = await realMem0Client.getMemories({ user_id: userId, limit: 1000 });
      const memoriesData = memories.memories || [];

      // 按分类统计
      const categoriesCount: Record<string, number> = {};
      memoriesData.forEach((memory: any) => {
        const categories = memory.custom_categories || ['未分类'];
        categories.forEach((category: string) => {
          categoriesCount[category] = (categoriesCount[category] || 0) + 1;
        });
      });

      // 活动时间线（最近30天）
      const timeline: Array<{date: string; memory_count: number}> = [];
      const now = new Date();
      for (let i = 29; i >= 0; i--) {
        const date = new Date(now);
        date.setDate(date.getDate() - i);
        const dateStr = date.toISOString().split('T')[0];

        const dayMemories = memoriesData.filter((memory: any) => {
          const memoryDate = new Date(memory.created_at).toISOString().split('T')[0];
          return memoryDate === dateStr;
        });

        timeline.push({
          date: dateStr,
          memory_count: dayMemories.length
        });
      }

      // 最活跃的天数
      const mostActiveDays = timeline
        .sort((a, b) => b.memory_count - a.memory_count)
        .slice(0, 5)
        .map(item => item.date);

      // 增长趋势分析
      const recentWeek = timeline.slice(-7);
      const previousWeek = timeline.slice(-14, -7);
      const recentTotal = recentWeek.reduce((sum, item) => sum + item.memory_count, 0);
      const previousTotal = previousWeek.reduce((sum, item) => sum + item.memory_count, 0);

      let trend: 'increasing' | 'decreasing' | 'stable' = 'stable';
      if (recentTotal > previousTotal * 1.1) trend = 'increasing';
      else if (recentTotal < previousTotal * 0.9) trend = 'decreasing';

      return {
        total_memories: memoriesData.length,
        memories_by_category: categoriesCount,
        activity_timeline: timeline,
        most_active_days: mostActiveDays,
        memory_growth_trend: trend
      };
    } catch (err) {
      console.error(`Failed to get analytics for user ${userId}:`, err);
      throw new Error('Failed to get user analytics');
    }
  }, []);

  // 导出用户数据
  const exportUserData = useCallback(async (userId: string): Promise<Blob> => {
    try {
      const memories = await realMem0Client.getMemories({ user_id: userId, limit: 1000 });
      const analytics = await getUserAnalytics(userId);

      const exportData = {
        user_id: userId,
        export_date: new Date().toISOString(),
        analytics,
        memories: memories.memories || []
      };

      const jsonString = JSON.stringify(exportData, null, 2);
      return new Blob([jsonString], { type: 'application/json' });
    } catch (err) {
      console.error(`Failed to export data for user ${userId}:`, err);
      throw new Error('Failed to export user data');
    }
  }, [getUserAnalytics]);

  // 批量用户操作
  const batchUserOperations = useCallback(async (operations: BatchUserOperation[]): Promise<void> => {
    setIsLoading(true);
    setError(null);

    try {
      for (const operation of operations) {
        switch (operation.type) {
          case 'delete':
            for (const userId of operation.userIds) {
              await realMem0Client.deleteAllMemories({ user_id: userId });
            }
            break;
          case 'export':
            // 批量导出会在前端处理，这里只是验证
            for (const userId of operation.userIds) {
              await getUserAnalytics(userId); // 验证用户存在
            }
            break;
          case 'archive':
            // 归档操作（标记为非活跃）
            for (const userId of operation.userIds) {
              const memories = await realMem0Client.getMemories({ user_id: userId, limit: 1000 });
              // 这里可以添加归档逻辑，比如添加特殊标记
            }
            break;
        }
      }

      // 刷新用户列表
      await fetchUsers();
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Batch operation failed';
      setError(errorMessage);
      throw new Error(errorMessage);
    } finally {
      setIsLoading(false);
    }
  }, [fetchUsers, getUserAnalytics]);

  // 获取用户记忆时间线
  const getUserMemoryTimeline = useCallback(async (userId: string, days = 30): Promise<Array<{date: string; count: number}>> => {
    try {
      const memories = await realMem0Client.getMemories({ user_id: userId, limit: 1000 });
      const memoriesData = memories.memories || [];

      const timeline: Array<{date: string; count: number}> = [];
      const now = new Date();

      for (let i = days - 1; i >= 0; i--) {
        const date = new Date(now);
        date.setDate(date.getDate() - i);
        const dateStr = date.toISOString().split('T')[0];

        const dayMemories = memoriesData.filter((memory: any) => {
          const memoryDate = new Date(memory.created_at).toISOString().split('T')[0];
          return memoryDate === dateStr;
        });

        timeline.push({
          date: dateStr,
          count: dayMemories.length
        });
      }

      return timeline;
    } catch (err) {
      console.error(`Failed to get timeline for user ${userId}:`, err);
      return [];
    }
  }, []);

  // 初始化时获取用户列表
  useEffect(() => {
    fetchUsers();
  }, [fetchUsers]);

  return {
    users,
    currentUserId,
    isLoading,
    error,
    setCurrentUserId,
    fetchUsers,
    getUserStats,
    createUser,
    deleteUser,
    // 新增的增强功能
    getUserAnalytics,
    exportUserData,
    batchUserOperations,
    getUserMemoryTimeline
  };
};
