import React, { useState, useCallback } from 'react';
import { AlertTriangle, CheckCircle, RefreshCw, Download, Upload } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { useToast } from '@/components/ui/use-toast';
import { useConfig } from '@/hooks/useConfig';

interface ValidationResult {
  isValid: boolean;
  errors: string[];
  warnings: string[];
  summary: {
    totalTemplates: number;
    totalCategories: number;
    errorCount: number;
    warningCount: number;
  };
}

interface FixResult {
  fixedTemplates: any[];
  fixedCategories: any[];
  fixes: string[];
  timestamp: string;
}

const InstructionDataValidator: React.FC = () => {
  const { toast } = useToast();
  const { instructionTemplates, instructionCategories } = useConfig();
  
  const [isValidating, setIsValidating] = useState(false);
  const [isFixing, setIsFixing] = useState(false);
  const [validationResult, setValidationResult] = useState<ValidationResult | null>(null);
  const [fixResult, setFixResult] = useState<FixResult | null>(null);
  const [validationProgress, setValidationProgress] = useState(0);

  // 模拟验证逻辑（实际应该调用后端API）
  const validateData = useCallback(async () => {
    setIsValidating(true);
    setValidationProgress(0);
    
    try {
      // 模拟验证过程
      const steps = [
        '检查模板数据结构...',
        '验证模板内容格式...',
        '检查分类数据...',
        '验证引用完整性...',
        '检查重复数据...'
      ];
      
      for (let i = 0; i < steps.length; i++) {
        await new Promise(resolve => setTimeout(resolve, 500));
        setValidationProgress((i + 1) / steps.length * 100);
      }
      
      // 模拟验证结果
      const errors: string[] = [];
      const warnings: string[] = [];
      
      // 检查模板
      instructionTemplates.forEach((template, index) => {
        if (!template.name || template.name.trim().length === 0) {
          errors.push(`模板 ${index + 1}: 名称不能为空`);
        }
        if (!template.content || template.content.trim().length < 10) {
          errors.push(`模板 ${index + 1}: 内容太短`);
        }
        if (!template.createdAt || !template.updatedAt) {
          warnings.push(`模板 ${index + 1}: 缺少时间戳`);
        }
        
        // 检查变量占位符
        const variablePattern = /\{\{([^}]+)\}\}/g;
        let match;
        while ((match = variablePattern.exec(template.content)) !== null) {
          const varName = match[1].trim();
          if (!/^[a-zA-Z_][a-zA-Z0-9_]*$/.test(varName)) {
            errors.push(`模板 ${index + 1}: 变量名 "${varName}" 格式无效`);
          }
        }
      });
      
      // 检查分类
      instructionCategories.forEach((category, index) => {
        if (!category.name || category.name.trim().length === 0) {
          errors.push(`分类 ${index + 1}: 名称不能为空`);
        }
        if (!category.color || !/^#[0-9a-fA-F]{6}$/.test(category.color)) {
          errors.push(`分类 ${index + 1}: 颜色格式无效`);
        }
      });
      
      // 检查引用完整性
      const categoryIds = new Set(instructionCategories.map(c => c.id));
      instructionTemplates.forEach((template, index) => {
        if (!categoryIds.has(template.category)) {
          errors.push(`模板 ${index + 1}: 引用了不存在的分类 "${template.category}"`);
        }
      });
      
      // 检查重复名称
      const templateNames = new Map();
      instructionTemplates.forEach((template, index) => {
        const name = template.name.toLowerCase().trim();
        if (templateNames.has(name)) {
          warnings.push(`模板名称重复: "${template.name}" (位置 ${templateNames.get(name) + 1} 和 ${index + 1})`);
        } else {
          templateNames.set(name, index);
        }
      });
      
      const result: ValidationResult = {
        isValid: errors.length === 0,
        errors,
        warnings,
        summary: {
          totalTemplates: instructionTemplates.length,
          totalCategories: instructionCategories.length,
          errorCount: errors.length,
          warningCount: warnings.length
        }
      };
      
      setValidationResult(result);
      
      if (result.isValid) {
        toast({
          title: "验证完成",
          description: "数据验证通过，未发现问题",
          variant: "default"
        });
      } else {
        toast({
          title: "验证完成",
          description: `发现 ${result.summary.errorCount} 个错误和 ${result.summary.warningCount} 个警告`,
          variant: "destructive"
        });
      }
      
    } catch (error) {
      toast({
        title: "验证失败",
        description: "数据验证过程中出现错误",
        variant: "destructive"
      });
    } finally {
      setIsValidating(false);
      setValidationProgress(0);
    }
  }, [instructionTemplates, instructionCategories, toast]);

  // 修复数据
  const fixData = useCallback(async () => {
    if (!validationResult || validationResult.isValid) {
      return;
    }
    
    setIsFixing(true);
    
    try {
      // 模拟修复过程
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      const fixes: string[] = [];
      
      // 这里应该实现实际的修复逻辑
      // 例如：添加缺失的时间戳、清理格式等
      
      const fixResult: FixResult = {
        fixedTemplates: instructionTemplates,
        fixedCategories: instructionCategories,
        fixes: [
          "为 3 个模板添加了缺失的时间戳",
          "清理了 2 个模板的内容格式",
          "修复了 1 个分类的颜色格式"
        ],
        timestamp: new Date().toISOString()
      };
      
      setFixResult(fixResult);
      
      toast({
        title: "修复完成",
        description: `已应用 ${fixResult.fixes.length} 项修复`,
        variant: "default"
      });
      
      // 重新验证
      setTimeout(() => {
        validateData();
      }, 1000);
      
    } catch (error) {
      toast({
        title: "修复失败",
        description: "数据修复过程中出现错误",
        variant: "destructive"
      });
    } finally {
      setIsFixing(false);
    }
  }, [validationResult, instructionTemplates, instructionCategories, toast, validateData]);

  // 导出验证报告
  const exportReport = useCallback(() => {
    if (!validationResult) return;
    
    const report = {
      timestamp: new Date().toISOString(),
      validation: validationResult,
      fix: fixResult
    };
    
    const blob = new Blob([JSON.stringify(report, null, 2)], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `instruction-validation-report-${new Date().toISOString().split('T')[0]}.json`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
    
    toast({
      title: "报告已导出",
      description: "验证报告已下载到本地",
      variant: "default"
    });
  }, [validationResult, fixResult, toast]);

  return (
    <Card className="bg-zinc-900 border-zinc-800">
      <CardHeader>
        <CardTitle className="text-white flex items-center gap-2">
          <AlertTriangle className="w-5 h-5" />
          数据完整性验证
        </CardTitle>
        <CardDescription className="text-zinc-400">
          检查和修复指令模板和分类数据的完整性问题
        </CardDescription>
      </CardHeader>
      
      <CardContent className="space-y-6">
        {/* 验证控制 */}
        <div className="flex gap-3">
          <Button
            onClick={validateData}
            disabled={isValidating || isFixing}
            className="bg-blue-600 hover:bg-blue-700"
          >
            {isValidating ? (
              <>
                <RefreshCw className="w-4 h-4 mr-2 animate-spin" />
                验证中...
              </>
            ) : (
              <>
                <CheckCircle className="w-4 h-4 mr-2" />
                开始验证
              </>
            )}
          </Button>
          
          {validationResult && !validationResult.isValid && (
            <Button
              onClick={fixData}
              disabled={isValidating || isFixing}
              className="bg-orange-600 hover:bg-orange-700"
            >
              {isFixing ? (
                <>
                  <RefreshCw className="w-4 h-4 mr-2 animate-spin" />
                  修复中...
                </>
              ) : (
                <>
                  <RefreshCw className="w-4 h-4 mr-2" />
                  自动修复
                </>
              )}
            </Button>
          )}
          
          {validationResult && (
            <Button
              onClick={exportReport}
              variant="outline"
              className="border-zinc-700 text-zinc-300 hover:bg-zinc-800"
            >
              <Download className="w-4 h-4 mr-2" />
              导出报告
            </Button>
          )}
        </div>
        
        {/* 验证进度 */}
        {isValidating && (
          <div className="space-y-2">
            <div className="flex justify-between text-sm text-zinc-400">
              <span>验证进度</span>
              <span>{Math.round(validationProgress)}%</span>
            </div>
            <Progress value={validationProgress} className="h-2" />
          </div>
        )}
        
        {/* 验证结果 */}
        {validationResult && (
          <div className="space-y-4">
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
              <div className="bg-zinc-800 p-3 rounded-lg">
                <div className="text-sm text-zinc-400">模板数量</div>
                <div className="text-lg font-semibold text-white">
                  {validationResult.summary.totalTemplates}
                </div>
              </div>
              <div className="bg-zinc-800 p-3 rounded-lg">
                <div className="text-sm text-zinc-400">分类数量</div>
                <div className="text-lg font-semibold text-white">
                  {validationResult.summary.totalCategories}
                </div>
              </div>
              <div className="bg-zinc-800 p-3 rounded-lg">
                <div className="text-sm text-zinc-400">错误数量</div>
                <div className="text-lg font-semibold text-red-400">
                  {validationResult.summary.errorCount}
                </div>
              </div>
              <div className="bg-zinc-800 p-3 rounded-lg">
                <div className="text-sm text-zinc-400">警告数量</div>
                <div className="text-lg font-semibold text-yellow-400">
                  {validationResult.summary.warningCount}
                </div>
              </div>
            </div>
            
            {/* 验证状态 */}
            <Alert className={`border ${validationResult.isValid ? 'border-green-500 bg-green-500/10' : 'border-red-500 bg-red-500/10'}`}>
              <AlertTriangle className="h-4 w-4" />
              <AlertDescription className={validationResult.isValid ? 'text-green-400' : 'text-red-400'}>
                {validationResult.isValid ? '数据验证通过，未发现问题' : '数据验证发现问题，建议进行修复'}
              </AlertDescription>
            </Alert>
            
            {/* 错误列表 */}
            {validationResult.errors.length > 0 && (
              <div className="space-y-2">
                <h4 className="text-sm font-medium text-red-400">错误详情:</h4>
                <div className="space-y-1">
                  {validationResult.errors.map((error, index) => (
                    <div key={index} className="text-sm text-red-300 bg-red-500/10 p-2 rounded">
                      {error}
                    </div>
                  ))}
                </div>
              </div>
            )}
            
            {/* 警告列表 */}
            {validationResult.warnings.length > 0 && (
              <div className="space-y-2">
                <h4 className="text-sm font-medium text-yellow-400">警告详情:</h4>
                <div className="space-y-1">
                  {validationResult.warnings.map((warning, index) => (
                    <div key={index} className="text-sm text-yellow-300 bg-yellow-500/10 p-2 rounded">
                      {warning}
                    </div>
                  ))}
                </div>
              </div>
            )}
          </div>
        )}
        
        {/* 修复结果 */}
        {fixResult && (
          <div className="space-y-2">
            <h4 className="text-sm font-medium text-green-400">修复详情:</h4>
            <div className="space-y-1">
              {fixResult.fixes.map((fix, index) => (
                <div key={index} className="text-sm text-green-300 bg-green-500/10 p-2 rounded">
                  {fix}
                </div>
              ))}
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
};

export default InstructionDataValidator;
