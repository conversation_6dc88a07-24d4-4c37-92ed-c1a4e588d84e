'use client';

import React, { useState, useEffect } from 'react';
import { Plus, Edit, Trash2, Eye, Copy, Tag, Folder, AlertCircle } from 'lucide-react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';

import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Badge } from '@/components/ui/badge';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle
} from '@/components/ui/dialog';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from '@/components/ui/select';
import { Label } from '@/components/ui/label';
import { Switch } from '@/components/ui/switch';
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { useToast } from '@/components/ui/use-toast';
import { useConfig } from '@/hooks/useConfig';
import { InstructionTemplate, InstructionCategory } from '@/store/configSlice';

// 验证schema
const instructionTemplateSchema = z.object({
  name: z.string()
    .min(1, '模板名称不能为空')
    .min(2, '模板名称至少需要2个字符')
    .max(100, '模板名称不能超过100个字符')
    .regex(/^[a-zA-Z0-9\u4e00-\u9fa5\s\-_]+$/, '模板名称只能包含字母、数字、中文、空格、连字符和下划线'),
  description: z.string()
    .max(500, '描述不能超过500个字符')
    .optional(),
  content: z.string()
    .min(1, '指令内容不能为空')
    .min(10, '指令内容至少需要10个字符')
    .max(5000, '指令内容不能超过5000个字符'),
  category: z.string()
    .min(1, '请选择一个分类'),
  tags: z.string()
    .optional()
    .refine((val) => {
      if (!val || val.trim() === '') return true;
      const tags = val.split(',').map(tag => tag.trim()).filter(Boolean);
      return tags.length <= 10;
    }, '标签数量不能超过10个')
    .refine((val) => {
      if (!val || val.trim() === '') return true;
      const tags = val.split(',').map(tag => tag.trim()).filter(Boolean);
      return tags.every(tag => tag.length <= 20);
    }, '每个标签长度不能超过20个字符')
    .refine((val) => {
      if (!val || val.trim() === '') return true;
      const tags = val.split(',').map(tag => tag.trim()).filter(Boolean);
      return tags.every(tag => /^[a-zA-Z0-9\u4e00-\u9fa5\-_]+$/.test(tag));
    }, '标签只能包含字母、数字、中文、连字符和下划线'),
  isActive: z.boolean()
});

type InstructionTemplateFormData = z.infer<typeof instructionTemplateSchema>;

interface InstructionTemplateEditorProps {
  template: InstructionTemplate | null;
  isOpen: boolean;
  onClose: () => void;
  onSave: (template: Omit<InstructionTemplate, 'id' | 'createdAt' | 'updatedAt'>) => void;
  categories: InstructionCategory[];
}

const InstructionTemplateEditor: React.FC<InstructionTemplateEditorProps> = ({
  template,
  isOpen,
  onClose,
  onSave,
  categories
}) => {
  const { toast } = useToast();

  const form = useForm<InstructionTemplateFormData>({
    resolver: zodResolver(instructionTemplateSchema),
    defaultValues: {
      name: '',
      description: '',
      content: '',
      category: 'general',
      tags: '',
      isActive: true
    }
  });

  // 当模板变化时重置表单
  useEffect(() => {
    if (template) {
      form.reset({
        name: template.name,
        description: template.description || '',
        content: template.content,
        category: template.category,
        tags: template.tags?.join(', ') || '',
        isActive: template.isActive
      });
    } else {
      form.reset({
        name: '',
        description: '',
        content: '',
        category: 'general',
        tags: '',
        isActive: true
      });
    }
  }, [template, form]);

  const onSubmit = (data: InstructionTemplateFormData) => {
    try {
      onSave({
        name: data.name.trim(),
        description: data.description?.trim() || '',
        content: data.content.trim(),
        category: data.category,
        tags: data.tags ? data.tags.split(',').map(tag => tag.trim()).filter(Boolean) : [],
        isActive: data.isActive
      });

      toast({
        title: "成功",
        description: template ? "指令模板已更新" : "指令模板已创建",
        variant: "default"
      });

      handleClose();
    } catch (error) {
      toast({
        title: "错误",
        description: "保存失败，请重试",
        variant: "destructive"
      });
    }
  };

  const handleClose = () => {
    form.reset();
    onClose();
  };

  // 检查变量占位符的有效性
  const validateVariables = (content: string) => {
    const variablePattern = /\{\{([^}]+)\}\}/g;
    const variables = [];
    let match;

    while ((match = variablePattern.exec(content)) !== null) {
      const varName = match[1].trim();
      if (!/^[a-zA-Z_][a-zA-Z0-9_]*$/.test(varName)) {
        return `变量名 "${varName}" 无效。变量名只能包含字母、数字和下划线，且不能以数字开头`;
      }
      variables.push(varName);
    }

    return null;
  };

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent className="bg-zinc-900 border-zinc-800 max-w-2xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="text-white flex items-center gap-2">
            <Edit className="w-5 h-5 text-[#00d4aa]" />
            {template ? '编辑指令模板' : '创建指令模板'}
          </DialogTitle>
          <DialogDescription className="text-zinc-400">
            创建或编辑可重用的指令模板，支持变量占位符
          </DialogDescription>
        </DialogHeader>

        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
            <div className="grid grid-cols-2 gap-4">
              <FormField
                control={form.control}
                name="name"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className="text-zinc-400">模板名称 *</FormLabel>
                    <FormControl>
                      <Input
                        placeholder="输入模板名称"
                        className="bg-zinc-800 border-zinc-700 text-white"
                        {...field}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="category"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className="text-zinc-400">分类 *</FormLabel>
                    <Select onValueChange={field.onChange} value={field.value}>
                      <FormControl>
                        <SelectTrigger className="bg-zinc-800 border-zinc-700 text-white">
                          <SelectValue placeholder="选择分类" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent className="bg-zinc-800 border-zinc-700">
                        {categories.map((category) => (
                          <SelectItem key={category.id} value={category.id}>
                            {category.name}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            <FormField
              control={form.control}
              name="description"
              render={({ field }) => (
                <FormItem>
                  <FormLabel className="text-zinc-400">描述</FormLabel>
                  <FormControl>
                    <Input
                      placeholder="简要描述这个模板的用途"
                      className="bg-zinc-800 border-zinc-700 text-white"
                      {...field}
                    />
                  </FormControl>
                  <FormDescription className="text-zinc-500">
                    可选，最多500个字符
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="content"
              render={({ field }) => (
                <FormItem>
                  <FormLabel className="text-zinc-400">指令内容 *</FormLabel>
                  <FormControl>
                    <Textarea
                      placeholder="输入指令内容，可以使用 {{变量名}} 作为占位符"
                      rows={6}
                      className="bg-zinc-800 border-zinc-700 text-white"
                      {...field}
                      onChange={(e) => {
                        field.onChange(e);
                        // 实时验证变量占位符
                        const error = validateVariables(e.target.value);
                        if (error) {
                          form.setError('content', { message: error });
                        } else {
                          form.clearErrors('content');
                        }
                      }}
                    />
                  </FormControl>
                  <FormDescription className="text-zinc-500">
                    使用 {`{{变量名}}`} 创建可替换的占位符。变量名只能包含字母、数字和下划线，且不能以数字开头
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="tags"
              render={({ field }) => (
                <FormItem>
                  <FormLabel className="text-zinc-400">标签</FormLabel>
                  <FormControl>
                    <Input
                      placeholder="用逗号分隔多个标签，如：AI,助手,自动化"
                      className="bg-zinc-800 border-zinc-700 text-white"
                      {...field}
                    />
                  </FormControl>
                  <FormDescription className="text-zinc-500">
                    可选，最多10个标签，每个标签最多20个字符
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="isActive"
              render={({ field }) => (
                <FormItem className="flex flex-row items-center justify-between rounded-lg border border-zinc-700 p-3">
                  <div className="space-y-0.5">
                    <FormLabel className="text-zinc-400">启用模板</FormLabel>
                    <FormDescription className="text-zinc-500">
                      启用后该模板将在指令列表中可见
                    </FormDescription>
                  </div>
                  <FormControl>
                    <Switch
                      checked={field.value}
                      onCheckedChange={field.onChange}
                    />
                  </FormControl>
                </FormItem>
              )}
            />

            {/* 表单验证状态提示 */}
            {!form.formState.isValid && form.formState.isSubmitted && (
              <Alert className="border-red-500 bg-red-500/10">
                <AlertCircle className="h-4 w-4" />
                <AlertDescription className="text-red-400">
                  请检查并修正表单中的错误
                </AlertDescription>
              </Alert>
            )}

            <DialogFooter>
              <Button type="button" variant="outline" onClick={handleClose}>
                取消
              </Button>
              <Button
                type="submit"
                className="bg-[#00d4aa] hover:bg-[#00d4aa]/90 text-black"
                disabled={form.formState.isSubmitting}
              >
                {form.formState.isSubmitting ? '保存中...' : (template ? '更新' : '创建')}
              </Button>
            </DialogFooter>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
};

interface InstructionPreviewProps {
  template: InstructionTemplate | null;
  isOpen: boolean;
  onClose: () => void;
  onPreview: (template: string, variables?: Record<string, string>) => string;
}

const InstructionPreview: React.FC<InstructionPreviewProps> = ({
  template,
  isOpen,
  onClose,
  onPreview
}) => {
  const { toast } = useToast();
  const [variables, setVariables] = useState<Record<string, string>>({});
  const [previewResult, setPreviewResult] = useState('');

  React.useEffect(() => {
    if (template && isOpen) {
      // 提取模板中的变量
      const variableMatches = template.content.match(/\{\{\s*(\w+)\s*\}\}/g);
      const extractedVars: Record<string, string> = {};
      
      if (variableMatches) {
        variableMatches.forEach(match => {
          const varName = match.replace(/\{\{\s*|\s*\}\}/g, '');
          if (!extractedVars[varName]) {
            extractedVars[varName] = '';
          }
        });
      }
      
      setVariables(extractedVars);
      setPreviewResult(onPreview(template.content, extractedVars));
    }
  }, [template, isOpen, onPreview]);

  const handleVariableChange = (varName: string, value: string) => {
    const newVariables = { ...variables, [varName]: value };
    setVariables(newVariables);
    if (template) {
      setPreviewResult(onPreview(template.content, newVariables));
    }
  };

  const handleCopyToClipboard = () => {
    navigator.clipboard.writeText(previewResult);
    toast({
      title: "已复制",
      description: "指令内容已复制到剪贴板",
      variant: "default"
    });
  };

  if (!template) return null;

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="bg-zinc-900 border-zinc-800 max-w-3xl">
        <DialogHeader>
          <DialogTitle className="text-white flex items-center gap-2">
            <Eye className="w-5 h-5 text-[#00d4aa]" />
            预览指令模板 - {template.name}
          </DialogTitle>
          <DialogDescription className="text-zinc-400">
            设置变量值并预览最终的指令内容
          </DialogDescription>
        </DialogHeader>
        
        <div className="space-y-4">
          {Object.keys(variables).length > 0 && (
            <div>
              <h3 className="text-sm font-medium text-zinc-400 mb-2">变量设置</h3>
              <div className="grid grid-cols-2 gap-4">
                {Object.entries(variables).map(([varName, value]) => (
                  <div key={varName}>
                    <Label htmlFor={varName} className="text-zinc-400">{varName}</Label>
                    <Input
                      id={varName}
                      value={value}
                      onChange={(e) => handleVariableChange(varName, e.target.value)}
                      placeholder={`输入 ${varName} 的值`}
                      className="bg-zinc-800 border-zinc-700 text-white"
                    />
                  </div>
                ))}
              </div>
            </div>
          )}

          <div>
            <div className="flex items-center justify-between mb-2">
              <h3 className="text-sm font-medium text-zinc-400">预览结果</h3>
              <Button
                variant="outline"
                size="sm"
                onClick={handleCopyToClipboard}
                className="border-zinc-700 hover:border-zinc-600"
              >
                <Copy className="w-4 h-4 mr-2" />
                复制
              </Button>
            </div>
            <Textarea
              value={previewResult}
              readOnly
              rows={8}
              className="bg-zinc-800 border-zinc-700 text-white"
            />
          </div>
        </div>

        <DialogFooter>
          <Button
            variant="outline"
            onClick={onClose}
            className="border-zinc-700"
          >
            关闭
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

export default function InstructionManager() {
  const { toast } = useToast();
  const {
    instructionTemplates,
    instructionCategories,
    createInstructionTemplate,
    updateInstructionTemplateById,
    deleteInstructionTemplateById,
    previewInstruction
  } = useConfig();

  const [searchTerm, setSearchTerm] = useState('');
  const [selectedCategory, setSelectedCategory] = useState<string>('all');
  const [showEditor, setShowEditor] = useState(false);
  const [showPreview, setShowPreview] = useState(false);
  const [editingTemplate, setEditingTemplate] = useState<InstructionTemplate | null>(null);
  const [previewingTemplate, setPreviewingTemplate] = useState<InstructionTemplate | null>(null);

  // 过滤模板
  const filteredTemplates = instructionTemplates.filter(template => {
    const matchesSearch = template.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         template.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         template.tags.some(tag => tag.toLowerCase().includes(searchTerm.toLowerCase()));
    const matchesCategory = selectedCategory === 'all' || template.category === selectedCategory;
    return matchesSearch && matchesCategory;
  });

  const handleCreateTemplate = () => {
    setEditingTemplate(null);
    setShowEditor(true);
  };

  const handleEditTemplate = (template: InstructionTemplate) => {
    setEditingTemplate(template);
    setShowEditor(true);
  };

  const handleSaveTemplate = (templateData: Omit<InstructionTemplate, 'id' | 'createdAt' | 'updatedAt'>) => {
    if (editingTemplate) {
      updateInstructionTemplateById(editingTemplate.id, templateData);
    } else {
      createInstructionTemplate(templateData);
    }
  };

  const handleDeleteTemplate = (template: InstructionTemplate) => {
    deleteInstructionTemplateById(template.id);
    toast({
      title: "成功",
      description: "指令模板已删除",
      variant: "default"
    });
  };

  const handlePreviewTemplate = (template: InstructionTemplate) => {
    setPreviewingTemplate(template);
    setShowPreview(true);
  };

  return (
    <div className="space-y-6">
      {/* 页面标题和操作 */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold text-white">指令管理</h2>
          <p className="text-zinc-400 mt-1">
            创建和管理可重用的指令模板
          </p>
        </div>
        <Button
          onClick={handleCreateTemplate}
          className="bg-[#00d4aa] hover:bg-[#00d4aa]/90 text-black"
        >
          <Plus className="w-4 h-4 mr-2" />
          创建模板
        </Button>
      </div>

      {/* 搜索和过滤 */}
      <Card className="bg-zinc-900 border-zinc-800">
        <CardContent className="p-4">
          <div className="flex gap-4 items-center">
            <div className="flex-1">
              <Input
                placeholder="搜索模板名称、描述或标签..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="bg-zinc-800 border-zinc-700 text-white"
              />
            </div>

            <Select value={selectedCategory} onValueChange={setSelectedCategory}>
              <SelectTrigger className="w-48 bg-zinc-800 border-zinc-700 text-white">
                <Folder className="w-4 h-4 mr-2" />
                <SelectValue />
              </SelectTrigger>
              <SelectContent className="bg-zinc-800 border-zinc-700">
                <SelectItem value="all">所有分类</SelectItem>
                {instructionCategories.map((category) => (
                  <SelectItem key={category.id} value={category.id}>
                    {category.name}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
        </CardContent>
      </Card>

      {/* 统计信息 */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card className="bg-zinc-900 border-zinc-800">
          <CardHeader className="pb-2">
            <CardTitle className="text-sm text-zinc-400">总模板数</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-white">{instructionTemplates.length}</div>
          </CardContent>
        </Card>

        <Card className="bg-zinc-900 border-zinc-800">
          <CardHeader className="pb-2">
            <CardTitle className="text-sm text-zinc-400">活跃模板</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-white">
              {instructionTemplates.filter(t => t.isActive).length}
            </div>
          </CardContent>
        </Card>

        <Card className="bg-zinc-900 border-zinc-800">
          <CardHeader className="pb-2">
            <CardTitle className="text-sm text-zinc-400">分类数量</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-white">{instructionCategories.length}</div>
          </CardContent>
        </Card>

        <Card className="bg-zinc-900 border-zinc-800">
          <CardHeader className="pb-2">
            <CardTitle className="text-sm text-zinc-400">当前筛选</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-white">{filteredTemplates.length}</div>
          </CardContent>
        </Card>
      </div>

      {/* 模板列表 */}
      <Card className="bg-zinc-900 border-zinc-800">
        <CardHeader>
          <CardTitle className="text-white">指令模板</CardTitle>
          <CardDescription className="text-zinc-400">
            管理您的指令模板库
          </CardDescription>
        </CardHeader>
        <CardContent>
          {filteredTemplates.length === 0 ? (
            <div className="text-center py-8 text-zinc-400">
              {searchTerm || selectedCategory !== 'all' ? '未找到匹配的模板' : '暂无指令模板'}
            </div>
          ) : (
            <div className="space-y-4">
              {filteredTemplates.map((template) => {
                const category = instructionCategories.find(c => c.id === template.category);

                return (
                  <div
                    key={template.id}
                    className="p-4 rounded-lg bg-zinc-800 hover:bg-zinc-700 transition-colors"
                  >
                    <div className="flex items-start justify-between">
                      <div className="flex-1">
                        <div className="flex items-center gap-2 mb-2">
                          <h3 className="font-medium text-white">{template.name}</h3>
                          {!template.isActive && (
                            <Badge variant="secondary" className="bg-zinc-700 text-zinc-300">
                              未启用
                            </Badge>
                          )}
                          {category && (
                            <Badge
                              variant="secondary"
                              className="bg-zinc-700 text-zinc-300"
                              style={{ backgroundColor: `${category.color}20`, color: category.color }}
                            >
                              {category.name}
                            </Badge>
                          )}
                        </div>

                        <p className="text-sm text-zinc-400 mb-2">{template.description}</p>

                        <div className="flex items-center gap-2 mb-2">
                          {template.tags.map((tag) => (
                            <Badge key={tag} variant="outline" className="border-zinc-600 text-zinc-300">
                              <Tag className="w-3 h-3 mr-1" />
                              {tag}
                            </Badge>
                          ))}
                        </div>

                        <p className="text-xs text-zinc-500">
                          创建于 {new Date(template.createdAt).toLocaleDateString()} •
                          更新于 {new Date(template.updatedAt).toLocaleDateString()}
                        </p>
                      </div>

                      <div className="flex gap-2 ml-4">
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => handlePreviewTemplate(template)}
                          className="border-zinc-700 hover:border-zinc-600"
                        >
                          <Eye className="w-4 h-4" />
                        </Button>
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => handleEditTemplate(template)}
                          className="border-zinc-700 hover:border-zinc-600"
                        >
                          <Edit className="w-4 h-4" />
                        </Button>
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => handleDeleteTemplate(template)}
                          className="border-zinc-700 hover:border-red-600 text-red-400"
                        >
                          <Trash2 className="w-4 h-4" />
                        </Button>
                      </div>
                    </div>
                  </div>
                );
              })}
            </div>
          )}
        </CardContent>
      </Card>

      {/* 模态框 */}
      <InstructionTemplateEditor
        template={editingTemplate}
        isOpen={showEditor}
        onClose={() => setShowEditor(false)}
        onSave={handleSaveTemplate}
        categories={instructionCategories}
      />

      <InstructionPreview
        template={previewingTemplate}
        isOpen={showPreview}
        onClose={() => setShowPreview(false)}
        onPreview={previewInstruction}
      />
    </div>
  );
}
