/**
 * 指令管理数据验证和修复测试
 * 测试指令模板和分类的数据完整性验证功能
 */

import { describe, it, expect, beforeEach, afterEach } from '@jest/globals';
import { z } from 'zod';
import { InstructionTemplate, InstructionCategory } from '@/store/configSlice';

// 模拟数据验证schema
const instructionTemplateSchema = z.object({
  name: z.string()
    .min(1, '模板名称不能为空')
    .min(2, '模板名称至少需要2个字符')
    .max(100, '模板名称不能超过100个字符')
    .regex(/^[a-zA-Z0-9\u4e00-\u9fa5\s\-_]+$/, '模板名称只能包含字母、数字、中文、空格、连字符和下划线'),
  description: z.string()
    .max(500, '描述不能超过500个字符')
    .optional(),
  content: z.string()
    .min(1, '指令内容不能为空')
    .min(10, '指令内容至少需要10个字符')
    .max(5000, '指令内容不能超过5000个字符'),
  category: z.string()
    .min(1, '请选择一个分类'),
  tags: z.string()
    .optional()
    .refine((val) => {
      if (!val || val.trim() === '') return true;
      const tags = val.split(',').map(tag => tag.trim()).filter(Boolean);
      return tags.length <= 10;
    }, '标签数量不能超过10个')
    .refine((val) => {
      if (!val || val.trim() === '') return true;
      const tags = val.split(',').map(tag => tag.trim()).filter(Boolean);
      return tags.every(tag => tag.length <= 20);
    }, '每个标签长度不能超过20个字符')
    .refine((val) => {
      if (!val || val.trim() === '') return true;
      const tags = val.split(',').map(tag => tag.trim()).filter(Boolean);
      return tags.every(tag => /^[a-zA-Z0-9\u4e00-\u9fa5\-_]+$/.test(tag));
    }, '标签只能包含字母、数字、中文、连字符和下划线'),
  isActive: z.boolean()
});

// 数据验证器类
class InstructionDataValidator {
  private templates: InstructionTemplate[] = [];
  private categories: InstructionCategory[] = [];
  private validationErrors: string[] = [];

  constructor(templates: InstructionTemplate[], categories: InstructionCategory[]) {
    this.templates = templates;
    this.categories = categories;
  }

  // 验证所有数据
  validateAll(): { isValid: boolean; errors: string[]; warnings: string[] } {
    this.validationErrors = [];
    const warnings: string[] = [];

    // 验证模板数据
    this.templates.forEach((template, index) => {
      try {
        this.validateTemplate(template);
      } catch (error) {
        this.validationErrors.push(`模板 ${index + 1}: ${error.message}`);
      }
    });

    // 验证分类数据
    this.categories.forEach((category, index) => {
      try {
        this.validateCategory(category);
      } catch (error) {
        this.validationErrors.push(`分类 ${index + 1}: ${error.message}`);
      }
    });

    // 验证引用完整性
    const referenceErrors = this.validateReferences();
    this.validationErrors.push(...referenceErrors);

    // 检查重复数据
    const duplicateWarnings = this.checkDuplicates();
    warnings.push(...duplicateWarnings);

    return {
      isValid: this.validationErrors.length === 0,
      errors: this.validationErrors,
      warnings
    };
  }

  // 验证单个模板
  private validateTemplate(template: InstructionTemplate): void {
    // 基础字段验证
    if (!template.id || typeof template.id !== 'string') {
      throw new Error('模板ID无效');
    }

    // 使用Zod schema验证
    const formData = {
      name: template.name,
      description: template.description || '',
      content: template.content,
      category: template.category,
      tags: template.tags?.join(', ') || '',
      isActive: template.isActive
    };

    instructionTemplateSchema.parse(formData);

    // 验证变量占位符
    this.validateVariables(template.content);

    // 验证时间戳
    if (!template.createdAt || !template.updatedAt) {
      throw new Error('缺少时间戳');
    }

    try {
      new Date(template.createdAt);
      new Date(template.updatedAt);
    } catch {
      throw new Error('时间戳格式无效');
    }
  }

  // 验证单个分类
  private validateCategory(category: InstructionCategory): void {
    if (!category.id || typeof category.id !== 'string') {
      throw new Error('分类ID无效');
    }

    if (!category.name || category.name.trim().length === 0) {
      throw new Error('分类名称不能为空');
    }

    if (category.name.length > 50) {
      throw new Error('分类名称不能超过50个字符');
    }

    if (category.description && category.description.length > 200) {
      throw new Error('分类描述不能超过200个字符');
    }

    // 验证颜色格式
    if (!category.color || !/^#[0-9a-fA-F]{6}$/.test(category.color)) {
      throw new Error('颜色格式无效，应为#RRGGBB格式');
    }
  }

  // 验证变量占位符
  private validateVariables(content: string): void {
    const variablePattern = /\{\{([^}]+)\}\}/g;
    let match;

    while ((match = variablePattern.exec(content)) !== null) {
      const varName = match[1].trim();
      if (!/^[a-zA-Z_][a-zA-Z0-9_]*$/.test(varName)) {
        throw new Error(`变量名 "${varName}" 无效。变量名只能包含字母、数字和下划线，且不能以数字开头`);
      }
    }
  }

  // 验证引用完整性
  private validateReferences(): string[] {
    const errors: string[] = [];
    const categoryIds = new Set(this.categories.map(c => c.id));

    this.templates.forEach((template, index) => {
      if (!categoryIds.has(template.category)) {
        errors.push(`模板 ${index + 1} 引用了不存在的分类: ${template.category}`);
      }
    });

    return errors;
  }

  // 检查重复数据
  private checkDuplicates(): string[] {
    const warnings: string[] = [];

    // 检查模板名称重复
    const templateNames = new Map<string, number>();
    this.templates.forEach((template, index) => {
      const name = template.name.toLowerCase().trim();
      if (templateNames.has(name)) {
        warnings.push(`模板名称重复: "${template.name}" (位置 ${templateNames.get(name)! + 1} 和 ${index + 1})`);
      } else {
        templateNames.set(name, index);
      }
    });

    // 检查分类名称重复
    const categoryNames = new Map<string, number>();
    this.categories.forEach((category, index) => {
      const name = category.name.toLowerCase().trim();
      if (categoryNames.has(name)) {
        warnings.push(`分类名称重复: "${category.name}" (位置 ${categoryNames.get(name)! + 1} 和 ${index + 1})`);
      } else {
        categoryNames.set(name, index);
      }
    });

    return warnings;
  }

  // 修复常见问题
  fixCommonIssues(): { fixed: InstructionTemplate[]; fixedCategories: InstructionCategory[]; fixes: string[] } {
    const fixes: string[] = [];
    const fixedTemplates = [...this.templates];
    const fixedCategories = [...this.categories];

    // 修复模板问题
    fixedTemplates.forEach((template, index) => {
      // 修复缺失的时间戳
      if (!template.createdAt) {
        template.createdAt = new Date().toISOString();
        fixes.push(`为模板 ${index + 1} 添加了创建时间`);
      }
      if (!template.updatedAt) {
        template.updatedAt = new Date().toISOString();
        fixes.push(`为模板 ${index + 1} 添加了更新时间`);
      }

      // 修复标签格式
      if (template.tags && Array.isArray(template.tags)) {
        template.tags = template.tags.map(tag => tag.trim()).filter(Boolean);
        fixes.push(`清理了模板 ${index + 1} 的标签格式`);
      }

      // 修复内容格式
      if (template.content) {
        template.content = template.content.trim();
      }
      if (template.description) {
        template.description = template.description.trim();
      }
    });

    return {
      fixed: fixedTemplates,
      fixedCategories,
      fixes
    };
  }
}

describe('指令管理数据验证', () => {
  let validator: InstructionDataValidator;
  let validTemplates: InstructionTemplate[];
  let validCategories: InstructionCategory[];

  beforeEach(() => {
    validCategories = [
      {
        id: 'general',
        name: '通用指令',
        description: '适用于各种场景的通用指令模板',
        color: '#00d4aa'
      },
      {
        id: 'memory',
        name: '记忆管理',
        description: '专门用于记忆创建和管理的指令',
        color: '#3b82f6'
      }
    ];

    validTemplates = [
      {
        id: 'template_1',
        name: '测试模板',
        description: '这是一个测试模板',
        content: '请根据 {{input}} 生成相应的回答',
        category: 'general',
        tags: ['测试', '示例'],
        isActive: true,
        createdAt: '2024-01-01T00:00:00.000Z',
        updatedAt: '2024-01-01T00:00:00.000Z'
      }
    ];

    validator = new InstructionDataValidator(validTemplates, validCategories);
  });

  describe('模板验证', () => {
    it('应该验证有效的模板数据', () => {
      const result = validator.validateAll();
      expect(result.isValid).toBe(true);
      expect(result.errors).toHaveLength(0);
    });

    it('应该检测无效的模板名称', () => {
      const invalidTemplates = [{
        ...validTemplates[0],
        name: ''
      }];
      
      validator = new InstructionDataValidator(invalidTemplates, validCategories);
      const result = validator.validateAll();
      
      expect(result.isValid).toBe(false);
      expect(result.errors.some(error => error.includes('模板名称不能为空'))).toBe(true);
    });

    it('应该检测无效的变量占位符', () => {
      const invalidTemplates = [{
        ...validTemplates[0],
        content: '请根据 {{123invalid}} 生成回答'
      }];
      
      validator = new InstructionDataValidator(invalidTemplates, validCategories);
      const result = validator.validateAll();
      
      expect(result.isValid).toBe(false);
      expect(result.errors.some(error => error.includes('变量名'))).toBe(true);
    });

    it('应该检测引用不存在的分类', () => {
      const invalidTemplates = [{
        ...validTemplates[0],
        category: 'nonexistent'
      }];
      
      validator = new InstructionDataValidator(invalidTemplates, validCategories);
      const result = validator.validateAll();
      
      expect(result.isValid).toBe(false);
      expect(result.errors.some(error => error.includes('引用了不存在的分类'))).toBe(true);
    });
  });

  describe('分类验证', () => {
    it('应该验证有效的分类数据', () => {
      const result = validator.validateAll();
      expect(result.isValid).toBe(true);
    });

    it('应该检测无效的分类颜色', () => {
      const invalidCategories = [{
        ...validCategories[0],
        color: 'invalid-color'
      }];
      
      validator = new InstructionDataValidator(validTemplates, invalidCategories);
      const result = validator.validateAll();
      
      expect(result.isValid).toBe(false);
      expect(result.errors.some(error => error.includes('颜色格式无效'))).toBe(true);
    });
  });

  describe('重复检测', () => {
    it('应该检测重复的模板名称', () => {
      const duplicateTemplates = [
        validTemplates[0],
        { ...validTemplates[0], id: 'template_2' }
      ];
      
      validator = new InstructionDataValidator(duplicateTemplates, validCategories);
      const result = validator.validateAll();
      
      expect(result.warnings.some(warning => warning.includes('模板名称重复'))).toBe(true);
    });
  });

  describe('数据修复', () => {
    it('应该修复缺失的时间戳', () => {
      const brokenTemplates = [{
        ...validTemplates[0],
        createdAt: '',
        updatedAt: ''
      }];
      
      validator = new InstructionDataValidator(brokenTemplates, validCategories);
      const result = validator.fixCommonIssues();
      
      expect(result.fixes.some(fix => fix.includes('添加了创建时间'))).toBe(true);
      expect(result.fixes.some(fix => fix.includes('添加了更新时间'))).toBe(true);
      expect(result.fixed[0].createdAt).toBeTruthy();
      expect(result.fixed[0].updatedAt).toBeTruthy();
    });
  });
});
