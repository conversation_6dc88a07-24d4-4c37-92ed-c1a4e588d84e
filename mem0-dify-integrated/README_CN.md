# Mem0开源插件 for Dify

*English documentation: [README.md](README.md)*

## 🚀 概述

**Mem0开源插件**是为Dify平台提供的全面记忆管理解决方案，具备高级对话存储和AI驱动的记忆提取功能。该插件支持智能记忆管理，包括多模态内容支持、语义搜索和复杂的记忆操作。

**作者:** mocha（二次开发）
**原作者:** yevanchen
**版本:** 0.2.0
**类型:** 记忆管理插件

## 🙏 致谢

本插件基于 **yevanchen** 的原创作品进行二次开发。我们对其奠定的基础实现和创新的Dify记忆管理方法表示诚挚的感谢。本次二次开发在这个优秀的基础上构建，提供了增强的功能和改进的用户体验。

**原作者贡献:**
- 基础的记忆添加和搜索功能
- 初始Mem0 API集成基础
- Dify插件核心结构

**二次开发增强:**
- **自定义本地部署支持**：添加可配置API URL支持自托管Mem0实例
- **完整工具套件**：从基础添加/搜索扩展到8个综合工具
- **高级API集成**：完整的V1和V2 API支持及多模态功能
- **增强视觉设计**：emoji图标和改进的UI/UX组织
- **完整国际化**：4语言支持（中英日葡）
- **全面文档**：双语指南和开发者文档
- **性能优化**：增强的错误处理和响应处理

## ✨ 核心功能

### 📝 **记忆创建与管理**
- **💾 基础记忆存储**：通过AI推理存储对话记忆
- **🎨 多模态支持**：无缝处理文本、图像、文档和PDF
- **🧠 AI推理**：从对话中提取有意义的见解和上下文
- **🔗 关系映射**：自动发现记忆之间的关系

### 🔍 **高级搜索与检索**
- **🔍 语义搜索**：使用自然语言查询查找记忆
- **⚡ 高级过滤**：相似度阈值、元数据过滤器和分页
- **📋 批量操作**：高效检索和管理多个记忆
- **🎯 上下文匹配**：基于对话上下文的智能记忆匹配

### ⚙️ **记忆操作**
- **✏️ 记忆更新**：修改和增强现有记忆
- **🗑️ 安全删除**：移除不需要或过时的记忆
- **📜 版本历史**：跟踪所有变更和修改
- **🔄 记忆生命周期**：从创建到删除的完整记忆管理

### 🛡️ **安全与隐私**
- **🔐 用户隔离**：用户之间的完全数据分离
- **🔑 API认证**：通过API密钥管理的安全访问
- **🌐 灵活部署**：支持自托管和云解决方案
- **📊 审计日志**：全面的活动跟踪

## 🛠️ 安装

1. **下载**发布版本中的插件包
2. **上传**到您的Dify实例的插件管理界面
3. **配置**您的Mem0 API凭据：
   - **API密钥**：您的Mem0认证密钥
   - **API地址**：Mem0服务器端点（默认：`http://localhost:8000`）
4. **激活**插件并开始使用记忆管理工具

## 🔧 配置

### 必需设置
- **Mem0 API密钥**：Mem0服务的认证密钥
- **Mem0 API地址**：服务器端点（自托管或云端）

### 可选设置
- **默认推理模式**：默认启用/禁用AI推理
- **记忆保留策略**：配置自动清理规则
- **搜索偏好**：设置默认相似度阈值

## 📚 可用工具

| 工具 | 描述 | 使用场景 |
|------|------|----------|
| 💾 **添加记忆** | 通过AI推理存储对话记忆 | 基础记忆创建 |
| 🎨 **多模态记忆** | 处理图像、文档和PDF | 丰富内容存储 |
| 🔍 **检索记忆** | 使用关键词和语义搜索记忆 | 基础记忆检索 |
| 🔍⚡ **高级搜索** | 带相似度阈值的高级过滤 | 精确记忆查找 |
| 📋 **批量检索** | 获取多个记忆并支持排序和分页 | 记忆管理 |
| ✏️ **更新记忆** | 修改现有记忆内容 | 记忆维护 |
| 🗑️ **删除记忆** | 通过ID删除特定记忆 | 记忆清理 |
| 📜 **记忆历史** | 查看版本历史和变更 | 记忆跟踪 |

## 🎯 使用场景

### **个人AI助手**
- 记住用户偏好和过往对话
- 基于记忆提供上下文响应
- 随时间学习和适应用户行为

### **客户支持**
- 维护客户交互历史
- 跟踪问题解决和后续跟进
- 提供个性化支持体验

### **知识管理**
- 存储和组织重要信息
- 创建可搜索的知识库
- 维护机构记忆

### **内容创作**
- 记住写作风格和偏好
- 跟踪项目进展和想法
- 在会话间维护创作上下文

## 🔗 API集成

插件集成了Mem0的强大API：

- **V1 API**：基础记忆操作和搜索
- **V2 API**：带过滤和分页的高级功能
- **多模态API**：支持各种内容类型
- **历史API**：版本跟踪和变更管理

## 🌐 多语言支持

- **English**：完整的界面和文档
- **中文**：为中文用户提供完整本地化
- **日本語**：日语支持
- **Português**：葡萄牙语本地化

## 📖 文档

- **安装指南**：逐步设置说明
- **API参考**：完整的工具文档
- **最佳实践**：优化技巧和建议
- **故障排除**：常见问题和解决方案

## 🤝 支持

- **GitHub Issues**：报告错误和请求功能
- **文档**：全面的指南和示例
- **社区**：加入讨论并分享经验

## 🔧 二次开发

本插件是对原始Mem0插件的二次开发，增强了以下功能：

### **增强功能**
- **视觉设计**：添加了emoji图标和改进的UI/UX
- **国际化**：完整的4语言支持（中英日葡）
- **文档**：全面的双语文档
- **工具组织**：逻辑分类和改进的工作流程
- **错误处理**：增强的错误消息和用户反馈
- **性能**：优化的API调用和响应处理

### **开发方法**
- **尊重增强**：在原始架构基础上构建
- **向后兼容**：保持与现有工作流程的兼容性
- **社区专注**：开源开发和社区贡献
- **质量保证**：全面的测试和验证

### **贡献**
我们欢迎为进一步增强此插件做出贡献：
- **错误报告**：通过GitHub提交问题
- **功能请求**：提出新功能建议
- **代码贡献**：提交改进的拉取请求
- **文档**：帮助改进指南和示例

## 📄 许可证

此插件是开源的，采用MIT许可证。

---

**版本**: 0.2.0
**作者**: mocha（二次开发）
**原作者**: yevanchen
**最后更新**: 2025-07-24
