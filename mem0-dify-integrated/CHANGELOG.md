# Changelog / 更新日志

*This document is available in English and Chinese.*  
*本文档提供中英文版本。*

---

## 🇺🇸 English Version

### [0.2.0] - 2025-07-24

#### 🎉 Major Release - Plugin Information Enhancement

**🔧 Plugin Identity Updates**
- **Author**: Updated from `yevanchen` to `mocha`
- **Name**: Changed from `mem0_cloud` to `mem0_opensource`
- **Version**: Upgraded from `0.1.0` to `0.2.0`
- **Description**: Enhanced with open-source focus and comprehensive feature descriptions

**🎨 Visual & UX Improvements**
- **Tool Icons**: Added emoji icons for all 8 tools for better visual identification
- **Multi-language Labels**: Enhanced tool names with visual indicators
- **Logical Grouping**: Reorganized tools into functional categories:
  - 📝 Memory Creation & Management (2 tools)
  - 🔍 Memory Retrieval & Search (3 tools)  
  - ⚙️ Memory Operations (3 tools)

**🌐 Internationalization**
- **Complete Localization**: All tools now support 4 languages
  - English (en_US): Complete interface
  - Chinese (zh_Hans): Full localization
  - Japanese (ja_JP): Japanese support
  - Portuguese (pt_BR): Portuguese localization
- **Enhanced Descriptions**: Detailed, professional descriptions for all tools
- **Consistent Terminology**: Unified naming conventions across all languages

**📚 Documentation Overhaul**
- **README.md**: Complete rewrite with bilingual content (English/Chinese)
- **PRIVACY.md**: Comprehensive privacy policy in both languages
- **CHANGELOG.md**: Detailed version history and changes
- **Tool Documentation**: Enhanced descriptions and use cases for all tools

**🔧 Technical Improvements**
- **Configuration Cleanup**: Removed redundant tool references
- **Author Consistency**: All tools now have unified author information
- **Version Synchronization**: All version numbers aligned across files
- **Cache Cleanup**: Removed all Python cache files for clean deployment

**✨ Tool Enhancements**

| Tool | Old Label | New Label | Improvements |
|------|-----------|-----------|--------------|
| add_memory | Add Memory | 💾 Add Memory | Enhanced AI inference descriptions |
| multimodal_add | Add Multimodal Memory | 🎨 Multimodal Memory | Unified multimodal processing |
| retrieve_memory | Retrieve Memory | 🔍 Retrieve Memory | Semantic search emphasis |
| retrieve_memory_v2 | Retrieve Memory (V2) | 🔍⚡ Advanced Memory Search | Advanced features highlight |
| get_memories_v2 | Get Mem0 Memories V2 | 📋 Batch Memory Retrieval | Batch operations focus |
| update_memory | Update Mem0 Memory | ✏️ Update Memory | Content modification clarity |
| delete_memory | Delete Mem0 Memory | 🗑️ Delete Memory | Safe deletion emphasis |
| memory_history | Memory History | 📜 Memory History | Version tracking focus |

**🎯 User Experience**
- **Intuitive Icons**: Each tool has a unique emoji for quick identification
- **Clear Categories**: Tools grouped by functionality for better organization
- **Professional Descriptions**: Detailed explanations of each tool's purpose
- **Consistent Branding**: Unified visual identity across all components

#### 🔍 Testing & Validation

**✅ Comprehensive Testing**
- **End-to-End Testing**: All 8 tools tested successfully (100% pass rate)
- **Multimodal Testing**: Images, documents, and PDFs processing verified
- **Performance Testing**: Average response time 2.437s, all within acceptable limits
- **Configuration Validation**: All tool configurations verified for consistency

**🛡️ Quality Assurance**
- **Author Consistency**: 100% - All tools have unified author information
- **File Pairing**: 100% - All YAML configs have corresponding Python implementations
- **Multi-language**: 100% - All tools support 4 languages completely
- **Documentation**: 100% - Complete documentation coverage

#### 🚀 Deployment Ready

**📦 Package Optimization**
- **Clean Structure**: Organized file structure with logical grouping
- **Optimized Size**: Removed unnecessary files and cache
- **Version Alignment**: All version numbers synchronized
- **Documentation Complete**: Comprehensive guides and references

---

## 🇨🇳 中文版本

### [0.2.0] - 2025-07-24

#### 🎉 重大版本 - 插件信息完善

**🔧 插件身份更新**
- **作者**: 从 `yevanchen` 更新为 `mocha`
- **名称**: 从 `mem0_cloud` 更改为 `mem0_opensource`
- **版本**: 从 `0.1.0` 升级到 `0.2.0`
- **描述**: 增强开源特色和全面功能描述

**🎨 视觉与用户体验改进**
- **工具图标**: 为所有8个工具添加emoji图标，提升视觉识别度
- **多语言标签**: 增强工具名称的视觉指示器
- **逻辑分组**: 将工具重新组织为功能类别：
  - 📝 记忆创建与管理 (2个工具)
  - 🔍 记忆检索与搜索 (3个工具)
  - ⚙️ 记忆操作 (3个工具)

**🌐 国际化**
- **完整本地化**: 所有工具现在支持4种语言
  - 英文 (en_US): 完整界面
  - 中文 (zh_Hans): 完全本地化
  - 日文 (ja_JP): 日语支持
  - 葡萄牙文 (pt_BR): 葡萄牙语本地化
- **增强描述**: 所有工具的详细专业描述
- **统一术语**: 所有语言版本的统一命名规范

**📚 文档全面改进**
- **README.md**: 完全重写，提供双语内容（中英文）
- **PRIVACY.md**: 双语版本的全面隐私政策
- **CHANGELOG.md**: 详细的版本历史和变更记录
- **工具文档**: 增强所有工具的描述和使用案例

**🔧 技术改进**
- **配置清理**: 删除冗余的工具引用
- **作者一致性**: 所有工具现在具有统一的作者信息
- **版本同步**: 所有文件的版本号保持一致
- **缓存清理**: 删除所有Python缓存文件，确保干净部署

**✨ 工具增强**

| 工具 | 旧标签 | 新标签 | 改进内容 |
|------|--------|--------|----------|
| add_memory | 添加内存 | 💾 添加记忆 | 增强AI推理描述 |
| multimodal_add | 添加多模态记忆 | 🎨 多模态记忆 | 统一多模态处理 |
| retrieve_memory | 检索内存 | 🔍 检索记忆 | 强调语义搜索 |
| retrieve_memory_v2 | 检索内存 (V2) | 🔍⚡ 高级记忆搜索 | 突出高级功能 |
| get_memories_v2 | 获取Mem0记忆V2 | 📋 批量记忆获取 | 专注批量操作 |
| update_memory | 更新Mem0记忆 | ✏️ 更新记忆 | 明确内容修改 |
| delete_memory | 删除Mem0记忆 | 🗑️ 删除记忆 | 强调安全删除 |
| memory_history | 记忆历史 | 📜 记忆历史 | 专注版本跟踪 |

**🎯 用户体验**
- **直观图标**: 每个工具都有独特的emoji便于快速识别
- **清晰分类**: 按功能分组工具，便于组织管理
- **专业描述**: 每个工具用途的详细说明
- **统一品牌**: 所有组件的统一视觉标识

#### 🔍 测试与验证

**✅ 全面测试**
- **端到端测试**: 所有8个工具测试成功（100%通过率）
- **多模态测试**: 验证图像、文档和PDF处理
- **性能测试**: 平均响应时间2.437秒，均在可接受范围内
- **配置验证**: 验证所有工具配置的一致性

**🛡️ 质量保证**
- **作者一致性**: 100% - 所有工具具有统一的作者信息
- **文件配对**: 100% - 所有YAML配置都有对应的Python实现
- **多语言**: 100% - 所有工具完全支持4种语言
- **文档**: 100% - 完整的文档覆盖

#### 🚀 部署就绪

**📦 包优化**
- **清洁结构**: 有组织的文件结构和逻辑分组
- **优化大小**: 删除不必要的文件和缓存
- **版本对齐**: 所有版本号同步
- **文档完整**: 全面的指南和参考

---

### [0.1.0] - 2025-06-16

#### 🎉 Initial Release

**🚀 Core Features**
- Basic memory storage and retrieval
- V1 and V2 API support
- Multimodal content processing
- User isolation and security

**🛠️ Available Tools**
- Memory creation and management
- Search and retrieval capabilities
- Memory operations (update, delete)
- Version history tracking

**📚 Documentation**
- Basic setup instructions
- API reference
- Usage examples

---

**Maintained by**: mocha  
**Repository**: Mem0 Open Source Plugin for Dify  
**License**: MIT
