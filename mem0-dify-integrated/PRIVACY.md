# Privacy Policy / 隐私政策

*This document is available in English and Chinese. Please scroll down for the Chinese version.*
*本文档提供中英文版本，中文版本请向下滚动查看。*

---

## 🇺🇸 English Version

### Privacy Policy for Mem0 Open Source Plugin

This privacy policy describes how the Mem0 Open Source Plugin ("we", "our", or "us") collects, uses, and handles your information when you use our memory management plugin for Dify.

#### 📋 Information We Collect

We collect and store the following types of information:

**1. Conversation Data**
- Chat messages between users and AI assistants
- Message content for AI-powered memory extraction and semantic search
- Message metadata (timestamps, user roles, conversation context)
- Multimodal content (text, images, documents, PDFs when processed)

**2. User Identification & Context**
- User IDs for data partitioning and organization
- Agent IDs for conversation categorization (optional)
- Run IDs for session tracking (optional)
- Custom metadata for enhanced memory context (optional)

**3. Memory Metadata**
- Memory creation and modification timestamps
- Memory importance scores and categories
- Relationship mappings between memories
- Version history for memory updates

#### 🔧 How We Use Your Information

The collected information is used exclusively for:
- **Memory Storage**: Storing and organizing conversation memories
- **Semantic Search**: Enabling intelligent memory retrieval based on context
- **AI Inference**: Extracting meaningful insights from conversations
- **User Isolation**: Maintaining separate memory spaces for different users
- **Memory Management**: Providing update, delete, and history tracking capabilities
- **Performance Optimization**: Improving search accuracy and response times

#### 🔒 Data Storage and Security

**Storage Infrastructure**
- Data is stored on your configured Mem0 server (self-hosted or cloud)
- All data is partitioned by user_id to ensure complete isolation
- Supports both local deployment and cloud-based storage options

**Security Measures**
- Industry-standard encryption for data transmission and storage
- API key-based authentication for all data access
- Role-based access control for different user types
- Secure communication protocols (HTTPS/TLS)

**Data Isolation**
- Each user's memories are completely isolated from others
- Agent-specific memory partitioning when applicable
- No cross-user data access or sharing

#### ⏰ Data Retention

**Retention Policy**
- Memories are retained until explicitly deleted by the user
- Users have full control over their memory data through provided tools
- Memory history is maintained for version tracking and rollback capabilities

**User Control**
- Users can delete individual memories or entire memory collections
- Memory update history can be accessed and managed
- Bulk memory operations are supported for data management

**Data Portability**
- Users can export their memory data in standard formats
- Memory data can be migrated between different Mem0 instances
- Full backup and restore capabilities available

#### 🤝 Third-Party Access and Sharing

**No Data Selling**
- We do not sell, rent, or share your memory data with third parties
- Your conversation memories remain private and confidential

**Service Operations**
- Data access is limited to essential plugin operations only
- All API calls require proper authentication and authorization
- Logging is limited to operational metrics, not content

**AI Processing**
- External AI services (if configured) only process data for memory extraction
- No conversation content is stored by external AI providers
- AI processing is performed in real-time without data retention

#### 🔄 Data Processing and AI Integration

**Memory Extraction**
- AI models analyze conversations to extract meaningful memories
- Processing focuses on key information, preferences, and context
- Personal identifiers are handled with extra care and protection

**Multimodal Processing**
- Images, documents, and PDFs are processed for content extraction
- Processed content is stored as text-based memories
- Original files are not permanently stored unless explicitly configured

**Search and Retrieval**
- Semantic search uses AI embeddings for intelligent memory matching
- Search queries and results are not logged or stored
- All search operations respect user data isolation

#### 📱 Plugin-Specific Considerations

**Dify Integration**
- The plugin operates within the Dify platform's security framework
- Dify's privacy policies also apply to plugin usage
- Plugin data is separate from Dify's core platform data

**Configuration Privacy**
- API keys and configuration data are stored securely
- Configuration changes are logged for audit purposes
- Sensitive configuration data is encrypted

#### 🔄 Updates and Changes

**Policy Updates**
- We may update this privacy policy to reflect new features or legal requirements
- Users will be notified of material changes through plugin update notifications
- Continued use of the plugin constitutes acceptance of updated policies

**Feature Updates**
- New memory management features may introduce additional data processing
- Users will be informed of any new data collection or processing activities
- Opt-out mechanisms will be provided where applicable

#### 📞 Contact and Support

If you have questions about this privacy policy, your data, or need support:

**Technical Support**
- GitHub Issues: [Mem0 Plugin Repository]
- Documentation: Available in plugin package

**Privacy Inquiries**
- Email: <EMAIL>
- Response time: Within 48 hours for privacy-related inquiries

**Data Requests**
- Data export, deletion, or modification requests
- Account deactivation and data removal
- Privacy compliance assistance

---

## 🇨🇳 中文版本

### Mem0开源插件隐私政策

本隐私政策描述了Mem0开源插件（"我们"、"我们的"或"本插件"）在您使用我们为Dify平台开发的记忆管理插件时如何收集、使用和处理您的信息。

#### 📋 我们收集的信息

我们收集和存储以下类型的信息：

**1. 对话数据**
- 用户与AI助手之间的聊天消息
- 用于AI驱动的记忆提取和语义搜索的消息内容
- 消息元数据（时间戳、用户角色、对话上下文）
- 多模态内容（处理时的文本、图像、文档、PDF）

**2. 用户身份和上下文**
- 用于数据分区和组织的用户ID
- 用于对话分类的代理ID（可选）
- 用于会话跟踪的运行ID（可选）
- 用于增强记忆上下文的自定义元数据（可选）

**3. 记忆元数据**
- 记忆创建和修改时间戳
- 记忆重要性评分和分类
- 记忆之间的关系映射
- 记忆更新的版本历史

#### 🔧 我们如何使用您的信息

收集的信息专门用于：
- **记忆存储**：存储和组织对话记忆
- **语义搜索**：基于上下文实现智能记忆检索
- **AI推理**：从对话中提取有意义的见解
- **用户隔离**：为不同用户维护独立的记忆空间
- **记忆管理**：提供更新、删除和历史跟踪功能
- **性能优化**：提高搜索准确性和响应时间

#### 🔒 数据存储和安全

**存储基础设施**
- 数据存储在您配置的Mem0服务器上（自托管或云端）
- 所有数据按user_id分区以确保完全隔离
- 支持本地部署和基于云的存储选项

**安全措施**
- 数据传输和存储采用行业标准加密
- 基于API密钥的身份验证用于所有数据访问
- 针对不同用户类型的基于角色的访问控制
- 安全通信协议（HTTPS/TLS）

**数据隔离**
- 每个用户的记忆与其他用户完全隔离
- 适用时的代理特定记忆分区
- 无跨用户数据访问或共享

#### ⏰ 数据保留

**保留政策**
- 记忆保留直到用户明确删除
- 用户通过提供的工具完全控制其记忆数据
- 维护记忆历史以支持版本跟踪和回滚功能

**用户控制**
- 用户可以删除单个记忆或整个记忆集合
- 可以访问和管理记忆更新历史
- 支持批量记忆操作进行数据管理

**数据可移植性**
- 用户可以以标准格式导出其记忆数据
- 记忆数据可以在不同Mem0实例之间迁移
- 提供完整的备份和恢复功能

#### 🤝 第三方访问和共享

**不出售数据**
- 我们不向第三方出售、租赁或共享您的记忆数据
- 您的对话记忆保持私密和机密

**服务运营**
- 数据访问仅限于基本插件操作
- 所有API调用都需要适当的身份验证和授权
- 日志记录仅限于操作指标，不包含内容

**AI处理**
- 外部AI服务（如果配置）仅处理数据以进行记忆提取
- 外部AI提供商不存储对话内容
- AI处理实时进行，无数据保留

#### 🔄 数据处理和AI集成

**记忆提取**
- AI模型分析对话以提取有意义的记忆
- 处理重点关注关键信息、偏好和上下文
- 个人标识符得到额外的关注和保护

**多模态处理**
- 处理图像、文档和PDF以进行内容提取
- 处理后的内容存储为基于文本的记忆
- 除非明确配置，否则不永久存储原始文件

**搜索和检索**
- 语义搜索使用AI嵌入进行智能记忆匹配
- 搜索查询和结果不被记录或存储
- 所有搜索操作都尊重用户数据隔离

#### 📱 插件特定考虑

**Dify集成**
- 插件在Dify平台的安全框架内运行
- Dify的隐私政策也适用于插件使用
- 插件数据与Dify核心平台数据分离

**配置隐私**
- API密钥和配置数据安全存储
- 配置更改被记录用于审计目的
- 敏感配置数据经过加密

#### 🔄 更新和变更

**政策更新**
- 我们可能会更新此隐私政策以反映新功能或法律要求
- 用户将通过插件更新通知了解重大变更
- 继续使用插件即表示接受更新的政策

**功能更新**
- 新的记忆管理功能可能引入额外的数据处理
- 用户将被告知任何新的数据收集或处理活动
- 在适用的情况下将提供退出机制

#### 📞 联系和支持

如果您对此隐私政策、您的数据有疑问或需要支持：

**技术支持**
- GitHub问题：[Mem0插件仓库]
- 文档：插件包中提供

**隐私咨询**
- 邮箱：<EMAIL>
- 响应时间：隐私相关咨询48小时内回复

**数据请求**
- 数据导出、删除或修改请求
- 账户停用和数据移除
- 隐私合规协助

---

**Last updated / 最后更新**: 2025-07-24
**Version / 版本**: 0.2.0
**Plugin Version / 插件版本**: 0.2.0
**Author / 作者**: mocha
