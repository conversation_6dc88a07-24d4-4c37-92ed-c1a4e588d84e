# Development Guide / 开发指南

*This document is available in English and Chinese.*  
*本文档提供中英文版本。*

---

## 🇺🇸 English Version

### 🔧 Secondary Development Overview

This plugin represents a respectful secondary development of the original Mem0 plugin created by **yevan<PERSON>**. Our approach focuses on enhancing the existing functionality while maintaining the core architecture and ensuring backward compatibility.

### 🙏 Original Author Acknowledgment

**Original Author**: yevanchen
**Original Contributions**:
- Basic memory add and search functionality
- Initial Mem0 API integration foundation
- Core plugin structure for Dify

We deeply appreciate the innovative work and solid foundation provided by the original author. This secondary development builds upon that excellent base to provide enhanced user experience and additional features.

### 🎯 Secondary Development Goals

#### **Enhancement Areas**
1. **User Experience**: Improved visual design with emoji icons and better organization
2. **Internationalization**: Complete 4-language support (EN/CN/JP/PT)
3. **Documentation**: Comprehensive bilingual documentation and guides
4. **Code Quality**: Enhanced error handling and user feedback
5. **Performance**: Optimized API calls and response handling
6. **Maintainability**: Better code organization and documentation

#### **Principles**
- **Respectful Enhancement**: Building upon, not replacing, the original work
- **Backward Compatibility**: Ensuring existing workflows continue to work
- **Open Source Spirit**: Contributing improvements back to the community
- **Quality Focus**: Comprehensive testing and validation

### 🛠️ Development Environment Setup

#### **Prerequisites**
- Python 3.8+
- Dify development environment
- Mem0 server (local or cloud)
- Git for version control

#### **Local Development**
```bash
# Clone the repository
git clone <repository-url>
cd mem0-dify-integrated

# Install dependencies
pip install -r requirements.txt

# Set up environment variables
cp .env.example .env
# Edit .env with your configuration

# Run tests
python -m pytest tests/

# Validate configuration
python verify_plugin_signature.py
```

### 📁 Project Structure

```
mem0-dify-integrated/
├── manifest.yaml              # Plugin manifest
├── README.md                  # English documentation
├── README_CN.md              # Chinese documentation
├── DEVELOPMENT.md             # This development guide
├── PRIVACY.md                 # Privacy policy
├── CHANGELOG.md               # Version history
├── requirements.txt           # Python dependencies
├── provider/
│   └── mem0.yaml             # Provider configuration
└── tools/                    # Tool implementations
    ├── add_memory.yaml       # Basic memory addition
    ├── add_memory.py
    ├── multimodal_add.yaml   # Multimodal content
    ├── multimodal_add.py
    ├── retrieve_memory.yaml  # Basic retrieval
    ├── retrieve_memory.py
    ├── retrieve_memory_v2.yaml # Advanced search
    ├── retrieve_memory_v2.py
    ├── get_memories_v2.yaml  # Batch operations
    ├── get_memories_v2.py
    ├── update_memory.yaml    # Memory updates
    ├── update_memory.py
    ├── delete_memory.yaml    # Memory deletion
    ├── delete_memory.py
    ├── memory_history.yaml   # Version history
    └── memory_history.py
```

### 🔄 Development Workflow

#### **Making Changes**
1. **Understand Original**: Study the original implementation
2. **Plan Enhancement**: Design improvements that respect the original architecture
3. **Implement**: Make changes with proper testing
4. **Document**: Update documentation for any changes
5. **Test**: Comprehensive testing including backward compatibility
6. **Review**: Code review focusing on quality and compatibility

#### **Testing Strategy**
- **Unit Tests**: Individual tool functionality
- **Integration Tests**: API integration and workflow testing
- **Compatibility Tests**: Ensure backward compatibility
- **Performance Tests**: Response time and resource usage
- **User Experience Tests**: End-to-end user scenarios

### 🌐 Internationalization

#### **Supported Languages**
- **English (en_US)**: Primary language
- **Chinese (zh_Hans)**: Complete localization
- **Japanese (ja_JP)**: Interface support
- **Portuguese (pt_BR)**: Interface support

#### **Adding New Languages**
1. Update tool YAML files with new language labels
2. Add descriptions in the new language
3. Update documentation
4. Test the new language interface

### 📚 Documentation Standards

#### **Documentation Requirements**
- **Bilingual**: English and Chinese for all major documents
- **Comprehensive**: Cover installation, configuration, usage, and troubleshooting
- **User-Friendly**: Clear examples and step-by-step guides
- **Up-to-Date**: Synchronized with code changes

#### **Documentation Files**
- `README.md`: English project overview
- `README_CN.md`: Chinese project overview
- `DEVELOPMENT.md`: This development guide
- `PRIVACY.md`: Privacy policy (bilingual)
- `CHANGELOG.md`: Version history (bilingual)

### 🤝 Contributing Guidelines

#### **How to Contribute**
1. **Fork** the repository
2. **Create** a feature branch
3. **Make** your changes following our standards
4. **Test** thoroughly
5. **Document** your changes
6. **Submit** a pull request

#### **Contribution Areas**
- **Bug Fixes**: Report and fix issues
- **Feature Enhancements**: Propose and implement new features
- **Documentation**: Improve guides and examples
- **Testing**: Add test cases and improve coverage
- **Internationalization**: Add support for new languages

### 🔍 Code Review Process

#### **Review Criteria**
- **Functionality**: Does it work as intended?
- **Compatibility**: Maintains backward compatibility?
- **Quality**: Follows coding standards?
- **Documentation**: Properly documented?
- **Testing**: Adequate test coverage?

### 📊 Quality Assurance

#### **Quality Metrics**
- **Test Coverage**: >90% for critical functionality
- **Performance**: Response times within acceptable limits
- **Compatibility**: Works with existing workflows
- **Documentation**: Complete and accurate
- **User Experience**: Intuitive and user-friendly

---

## 🇨🇳 中文版本

### 🔧 二次开发概述

本插件是对 **yevanchen** 创建的原始Mem0插件的尊重性二次开发。我们的方法专注于增强现有功能，同时保持核心架构并确保向后兼容性。

### 🙏 原作者致谢

**原作者**: yevanchen
**原创贡献**:
- 基础的记忆添加和搜索功能
- 初始Mem0 API集成基础
- Dify插件核心结构

我们深深感谢原作者提供的创新工作和坚实基础。本次二次开发在这个优秀的基础上构建，提供增强的用户体验和附加功能。

### 🎯 二次开发目标

#### **增强领域**
1. **用户体验**: 改进的视觉设计，包括emoji图标和更好的组织
2. **国际化**: 完整的4语言支持（中英日葡）
3. **文档**: 全面的双语文档和指南
4. **代码质量**: 增强的错误处理和用户反馈
5. **性能**: 优化的API调用和响应处理
6. **可维护性**: 更好的代码组织和文档

#### **原则**
- **尊重增强**: 在原始工作基础上构建，而非替换
- **向后兼容**: 确保现有工作流程继续工作
- **开源精神**: 将改进贡献回社区
- **质量专注**: 全面的测试和验证

### 🛠️ 开发环境设置

#### **先决条件**
- Python 3.8+
- Dify开发环境
- Mem0服务器（本地或云端）
- Git版本控制

#### **本地开发**
```bash
# 克隆仓库
git clone <repository-url>
cd mem0-dify-integrated

# 安装依赖
pip install -r requirements.txt

# 设置环境变量
cp .env.example .env
# 编辑.env文件配置

# 运行测试
python -m pytest tests/

# 验证配置
python verify_plugin_signature.py
```

### 📁 项目结构

```
mem0-dify-integrated/
├── manifest.yaml              # 插件清单
├── README.md                  # 英文文档
├── README_CN.md              # 中文文档
├── DEVELOPMENT.md             # 开发指南
├── PRIVACY.md                 # 隐私政策
├── CHANGELOG.md               # 版本历史
├── requirements.txt           # Python依赖
├── provider/
│   └── mem0.yaml             # 提供商配置
└── tools/                    # 工具实现
    ├── add_memory.yaml       # 基础记忆添加
    ├── add_memory.py
    ├── multimodal_add.yaml   # 多模态内容
    ├── multimodal_add.py
    ├── retrieve_memory.yaml  # 基础检索
    ├── retrieve_memory.py
    ├── retrieve_memory_v2.yaml # 高级搜索
    ├── retrieve_memory_v2.py
    ├── get_memories_v2.yaml  # 批量操作
    ├── get_memories_v2.py
    ├── update_memory.yaml    # 记忆更新
    ├── update_memory.py
    ├── delete_memory.yaml    # 记忆删除
    ├── delete_memory.py
    ├── memory_history.yaml   # 版本历史
    └── memory_history.py
```

### 🤝 贡献指南

#### **如何贡献**
1. **Fork** 仓库
2. **创建** 功能分支
3. **进行** 符合标准的更改
4. **彻底** 测试
5. **记录** 您的更改
6. **提交** 拉取请求

#### **贡献领域**
- **错误修复**: 报告和修复问题
- **功能增强**: 提出和实现新功能
- **文档**: 改进指南和示例
- **测试**: 添加测试用例和提高覆盖率
- **国际化**: 添加新语言支持

### 📊 质量保证

#### **质量指标**
- **测试覆盖率**: 关键功能>90%
- **性能**: 响应时间在可接受范围内
- **兼容性**: 与现有工作流程兼容
- **文档**: 完整准确
- **用户体验**: 直观友好

---

**Version / 版本**: 0.2.0  
**Author / 作者**: mocha (Secondary Development / 二次开发)  
**Original Author / 原作者**: blue  
**Last Updated / 最后更新**: 2025-07-24
